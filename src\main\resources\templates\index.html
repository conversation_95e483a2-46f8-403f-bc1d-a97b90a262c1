<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - SafeBox</title>

    <!-- CSS样式 -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <!-- 网站图标 -->
    <link rel="icon" type="image/x-icon" th:href="@{/favicon.ico}">
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <h1>🔐 SafeBox</h1>
            <p class="subtitle">安全密码管理系统</p>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <ul>
                <li><a th:href="@{/web/}" class="active">🏠 首页</a></li>
                <li><a th:href="@{/web/add}">➕ 添加密码</a></li>
                <li><a th:href="@{/web/master-password}">🔑 主密码管理</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container">
        <!-- 消息提示区域 -->
        <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
        <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>
        <div th:if="${info}" class="alert alert-info" th:text="${info}"></div>

        <!-- 页面内容 -->
        <!-- 统计信息 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" th:text="${totalCount}">0</div>
                <div class="stat-label">密码条目总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">🔐</div>
                <div class="stat-label">安全加密存储</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">⚡</div>
                <div class="stat-label">快速访问</div>
            </div>
        </div>

        <!-- 密码条目列表 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">密码条目列表</h2>
                <div class="action-buttons">
                    <a th:href="@{/web/add}" class="btn btn-primary">➕ 添加新密码</a>
                </div>
            </div>

            <div th:if="${#lists.isEmpty(entries)}" style="text-align: center; padding: 40px; color: #666;">
                <div style="font-size: 3em; margin-bottom: 20px;">📝</div>
                <h3>暂无密码条目</h3>
                <p>点击上方"添加新密码"按钮开始管理您的密码</p>
                <a th:href="@{/web/add}" class="btn btn-primary" style="margin-top: 20px;">立即添加</a>
            </div>

            <div th:if="${!#lists.isEmpty(entries)}">
                <table class="table">
                    <thead>
                        <tr>
                            <th>标题</th>
                            <th>用户名</th>
                            <th>密码</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="entry : ${entries}">
                            <td>
                                <strong th:text="${entry.title}">标题</strong>
                            </td>
                            <td>
                                <span th:text="${entry.username}">用户名</span>
                                <button class="btn-copy" th:data-text="${entry.username}" 
                                        style="margin-left: 10px; background: none; border: none; color: #667eea; cursor: pointer;"
                                        title="复制用户名">📋</button>
                            </td>
                            <td>
                                <div class="password-field" style="display: flex; align-items: center;">
                                    <input type="password" th:value="${entry.password}" readonly 
                                           style="border: none; background: none; width: 120px; font-family: monospace;">
                                    <button class="password-toggle" style="margin-left: 10px;">显示</button>
                                    <button class="btn-copy" th:data-text="${entry.password}" 
                                            style="margin-left: 10px; background: none; border: none; color: #667eea; cursor: pointer;"
                                            title="复制密码">📋</button>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 10px;">
                                    <a th:href="@{/web/edit/{title}(title=${entry.title})}" 
                                       class="btn btn-secondary btn-sm">✏️ 编辑</a>
                                    
                                    <form th:action="@{/web/delete/{title}(title=${entry.title})}" 
                                          method="post" style="display: inline;">
                                        <button type="submit" class="btn btn-danger btn-sm btn-delete" 
                                                th:data-title="${entry.title}">🗑️ 删除</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 快速操作面板 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">快速操作</h3>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <a th:href="@{/web/add}" class="btn btn-primary" style="padding: 20px; text-align: center;">
                    <div style="font-size: 2em; margin-bottom: 10px;">➕</div>
                    <div>添加新密码</div>
                </a>
                <a th:href="@{/web/master-password}" class="btn btn-secondary" style="padding: 20px; text-align: center;">
                    <div style="font-size: 2em; margin-bottom: 10px;">🔑</div>
                    <div>管理主密码</div>
                </a>
                <a href="#" onclick="location.reload()" class="btn btn-secondary" style="padding: 20px; text-align: center;">
                    <div style="font-size: 2em; margin-bottom: 10px;">🔄</div>
                    <div>刷新列表</div>
                </a>
            </div>
        </div>

        <!-- 安全提示 -->
        <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="card-header" style="border-color: rgba(255,255,255,0.2);">
                <h3 class="card-title" style="color: white;">🛡️ 安全提示</h3>
            </div>
            <ul style="list-style: none; padding: 0;">
                <li style="margin-bottom: 10px;">✅ 所有密码都经过 KeePass 标准加密存储</li>
                <li style="margin-bottom: 10px;">✅ 定期更换主密码以确保安全</li>
                <li style="margin-bottom: 10px;">✅ 使用强密码，包含大小写字母、数字和特殊字符</li>
                <li style="margin-bottom: 10px;">✅ 不要在不安全的网络环境下访问密码管理系统</li>
            </ul>
        </div>
    </main>

    <!-- 页面底部 -->
    <footer style="text-align: center; padding: 40px 0; color: #666; border-top: 1px solid #e1e5e9; margin-top: 50px;">
        <div class="container">
            <p>&copy; 2025 SafeBox. 基于 Spring Boot + Thymeleaf + KeePass 构建</p>
            <p style="font-size: 0.9em; margin-top: 10px;">
                <span>🔒 安全</span> |
                <span>🚀 高效</span> |
                <span>💡 智能</span>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script th:src="@{/js/app.js}"></script>

    <script>
        // 页面特定的JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            console.log('首页已加载，共有 ' + [[${totalCount}]] + ' 个密码条目');

            // 如果没有密码条目，显示欢迎信息
            if ([[${totalCount}]] === 0) {
                setTimeout(() => {
                    showToast('欢迎使用 SafeBox！开始添加您的第一个密码条目吧。', 'info');
                }, 1000);
            }
        });
    </script>
</body>
</html>
