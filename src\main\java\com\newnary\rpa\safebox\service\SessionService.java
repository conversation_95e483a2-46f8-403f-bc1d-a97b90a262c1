package com.newnary.rpa.safebox.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.linguafranca.pwdb.Credentials;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@Component
@Slf4j
public class SessionService {

    private final Cache<String, Credentials> sessionCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMillis(30))
            .maximumSize(50000)
            .build();


}
