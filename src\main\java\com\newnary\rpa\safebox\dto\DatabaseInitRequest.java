package com.newnary.rpa.safebox.dto;

import lombok.Data;

/**
 * 数据库初始化请求DTO
 * 用于接收用户设定的主密码来初始化KeePass数据库
 *
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@Data
public class DatabaseInitRequest {

    /**
     * 主密码
     * 用于加密KeePass数据库的主密码
     */
    private String masterPassword;

    /**
     * 确认主密码
     * 用于确认主密码输入正确
     */
    private String confirmPassword;

    /**
     * 数据库名称（可选）
     * 如果不提供，将使用默认名称
     */
    private String databaseName;

    /**
     * 数据库描述（可选）
     */
    private String description;

    /**
     * 是否强制重新初始化
     * 如果数据库已存在，是否强制覆盖
     */
    private boolean forceReinit = false;

    /**
     * 验证密码确认是否匹配
     *
     * @return 如果密码匹配返回true，否则返回false
     */
    public boolean isPasswordMatched() {
        if (masterPassword == null || confirmPassword == null) {
            return false;
        }
        return masterPassword.equals(confirmPassword);
    }

    /**
     * 获取数据库名称，如果未设置则返回默认名称
     *
     * @return 数据库名称
     */
    public String getDatabaseNameOrDefault() {
        return (databaseName != null && !databaseName.trim().isEmpty()) 
            ? databaseName.trim() 
            : "SafeBox密码库";
    }

    /**
     * 获取数据库描述，如果未设置则返回默认描述
     *
     * @return 数据库描述
     */
    public String getDescriptionOrDefault() {
        return (description != null && !description.trim().isEmpty()) 
            ? description.trim() 
            : "由SafeBox系统创建的安全密码库";
    }
}
