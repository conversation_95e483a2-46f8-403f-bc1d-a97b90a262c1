package com.newnary.rpa.safebox.controller.security;

import com.newnary.rpa.safebox.controller.security.request.AskRequest;
import com.newnary.rpa.safebox.controller.security.request.ResultRequest;
import com.newnary.rpa.safebox.controller.security.response.AskResult;
import com.newnary.rpa.safebox.dto.BaseResponse;
import com.newnary.rpa.safebox.service.SecurityRequestService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客户端请求密码
 *
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@RestController
@RequestMapping("/security-request")
public class SecurityRequestController {

    @Resource
    private SecurityRequestService securityRequestService;

    /**
     * 申请密码
     **/
    @PostMapping("ask")
    public BaseResponse<String> ask(@RequestBody AskRequest request) {
        String session = securityRequestService.ask(request);
        if (session == null) {
            return BaseResponse.fail("");
        }
        return BaseResponse.success(session);
    }

    /**
     * 获取结果
     **/
    @PostMapping("result")
    public BaseResponse<AskResult> result(@RequestBody ResultRequest request) {
        AskResult result = securityRequestService.result(request);
        return BaseResponse.success(result);
    }

}
