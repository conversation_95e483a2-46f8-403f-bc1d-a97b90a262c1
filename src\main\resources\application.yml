server:
  port: 8080

spring:
  thymeleaf:
    # 模板文件路径
    prefix: classpath:/templates/
    # 模板文件后缀
    suffix: .html
    # 模板文件编码
    encoding: UTF-8
    # 模板文件类型
    mode: HTML
    # 开发环境下禁用缓存
    cache: false
    # 检查模板是否存在
    check-template: true
    # 检查模板位置是否存在
    check-template-location: true
  # 静态资源配置
  web:
    resources:
      # 静态资源路径
      static-locations: classpath:/static/
      # 缓存时间（开发环境设为0）
      cache:
        period: 0

safebox:
  appIds:
    - fcm7mj3y
    - kkax8afy
  keepass:
    kdbxPath: D:\Workspace\IdeaProjects\safebox\src\main\resources
  ec-private-key: egTh+Vmp3IQUbNb7IWm0uJSOhurh98Vx5k6wBV9aCk0=
  ec-public-key: A8nVqa8mxukhtHth3tq8UX+K+KPG2LiK0WVHogiAA64e

