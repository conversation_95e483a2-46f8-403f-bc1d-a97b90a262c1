package com.newnary.rpa.safebox.service;

import com.newnary.rpa.safebox.config.SafeboxProperties;
import com.newnary.rpa.safebox.keepass.KeePassManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.linguafranca.pwdb.kdbx.KdbxCreds;
import org.linguafranca.pwdb.kdbx.jackson.JacksonDatabase;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@Service
@Slf4j
public class PasswordService {

    private final KeePassManager keePassManager = new KeePassManager();
    private final ConcurrentHashMap<String, JacksonDatabase> databaseMap = new ConcurrentHashMap<>();

    @Resource
    private SafeboxProperties safeboxProperties;

    /**
     * 获取数据库
     **/
    private JacksonDatabase loadDatabase(String appId, byte[] password) {
        if (!checkAppId(appId)) {
            return null;
        }

        try {
            return keePassManager.load(getFilePath(appId).toString(), new KdbxCreds(password));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private boolean checkAppId(String appId) {
        if (CollectionUtils.isEmpty(safeboxProperties.getAppIds())) {
            return false;
        }
        return safeboxProperties.getAppIds().contains(appId);
    }

    private Path getFilePath(String appId) {
        return Paths.get(safeboxProperties.getKeepass().getKdbxPath(), appId, "database2.kdbx").normalize();
    }

}
