package com.newnary.rpa.safebox.service;

import com.newnary.rpa.safebox.config.SafeboxProperties;
import com.newnary.rpa.safebox.dto.DatabaseInitRequest;
import com.newnary.rpa.safebox.dto.SecurityEntry;
import com.newnary.rpa.safebox.keepass.KeePassManager;
import lombok.extern.slf4j.Slf4j;
import org.linguafranca.pwdb.kdbx.KdbxCreds;
import org.linguafranca.pwdb.kdbx.jackson.JacksonDatabase;
import org.linguafranca.pwdb.kdbx.jackson.JacksonEntry;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 密码管理服务类
 * 提供密码条目的增删改查和主密码管理功能
 *
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@Service
@Slf4j
public class SecurityManagerService {

    @Resource
    private SafeboxProperties safeboxProperties;

    private final KeePassManager keepPassManager = new KeePassManager();

    /**
     * 默认数据库密码 - 实际项目中应从配置或环境变量读取
     */
    private static final String DEFAULT_PASSWORD = "123123";

    /**
     * 默认数据库文件名
     */
    private static final String DEFAULT_DB_FILE = "database.kdbx";

    /**
     * 获取数据库文件完整路径
     *
     * @return 数据库文件路径
     */
    private String getDatabaseFilePath() {
        return Paths.get(safeboxProperties.getKeepass().getKdbxPath(), DEFAULT_DB_FILE).toString();
    }

    /**
     * 加载KeePass数据库
     *
     * @param password 数据库密码
     * @return 数据库对象
     * @throws IOException 加载失败时抛出异常
     */
    private JacksonDatabase loadDatabase(String password) throws IOException {
        String filePath = getDatabaseFilePath();
        KdbxCreds creds = new KdbxCreds(password.getBytes());
        return keepPassManager.load(filePath, creds);
    }

    /**
     * 保存KeePass数据库
     *
     * @param database 数据库对象
     * @param password 数据库密码
     * @throws IOException 保存失败时抛出异常
     */
    private void saveDatabase(JacksonDatabase database, String password) throws IOException {
        String filePath = getDatabaseFilePath();
        KdbxCreds creds = new KdbxCreds(password.getBytes());
        keepPassManager.save(database, filePath, creds);
    }

    /**
     * 将JacksonEntry转换为SecurityEntry DTO
     *
     * @param entry KeePass条目对象
     * @return SecurityEntry DTO对象
     */
    private SecurityEntry convertToSecurityEntry(JacksonEntry entry) {
        SecurityEntry securityEntry = new SecurityEntry();
        securityEntry.setTitle(entry.getTitle());
        securityEntry.setUsername(entry.getUsername());
        securityEntry.setPassword(entry.getPassword());
        return securityEntry;
    }

    /**
     * 获取所有密码条目
     *
     * @return 密码条目列表
     * @throws IOException 数据库操作异常
     */
    public List<SecurityEntry> getAllEntries() throws IOException {
        log.info("开始获取所有密码条目");

        JacksonDatabase database = loadDatabase(DEFAULT_PASSWORD);
        List<JacksonEntry> entries = keepPassManager.getAllEntries(database);

        List<SecurityEntry> result = entries.stream()
                .map(this::convertToSecurityEntry)
                .collect(Collectors.toList());

        log.info("成功获取{}个密码条目", result.size());
        return result;
    }

    /**
     * 保存密码条目（新增或更新）
     *
     * @param securityEntry 密码条目信息
     * @return 是否操作成功
     * @throws IOException 数据库操作异常
     */
    public boolean saveEntry(SecurityEntry securityEntry) throws IOException {
        log.info("开始保存密码条目，标题：{}", securityEntry.getTitle());

        if (securityEntry.getTitle() == null || securityEntry.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("条目标题不能为空");
        }

        JacksonDatabase database = loadDatabase(DEFAULT_PASSWORD);
        Optional<JacksonEntry> existingEntry = keepPassManager.getEntryByTitle(database, securityEntry.getTitle());

        if (existingEntry.isPresent()) {
            // 更新现有条目
            log.info("更新现有条目：{}", securityEntry.getTitle());
            JacksonEntry entry = existingEntry.get();
            entry.setTitle(securityEntry.getTitle());
            entry.setUsername(securityEntry.getUsername());
            entry.setPassword(securityEntry.getPassword());
        } else {
            // 创建新条目
            log.info("创建新条目：{}", securityEntry.getTitle());
            JacksonEntry entry = JacksonEntry.createEntry(database);
            entry.setTitle(securityEntry.getTitle());
            entry.setUsername(securityEntry.getUsername());
            entry.setPassword(securityEntry.getPassword());

            // 添加到根组
            database.getRootGroup().addEntry(entry);
        }

        saveDatabase(database, DEFAULT_PASSWORD);
        log.info("成功保存密码条目：{}", securityEntry.getTitle());
        return true;
    }

    /**
     * 删除密码条目
     *
     * @param title 条目标题
     * @return 是否删除成功
     * @throws IOException 数据库操作异常
     */
    public boolean deleteEntry(String title) throws IOException {
        log.info("开始删除密码条目，标题：{}", title);

        if (title == null || title.trim().isEmpty()) {
            throw new IllegalArgumentException("条目标题不能为空");
        }

        JacksonDatabase database = loadDatabase(DEFAULT_PASSWORD);
        Optional<JacksonEntry> entry = keepPassManager.getEntryByTitle(database, title);

        if (entry.isPresent()) {
            boolean deleted = keepPassManager.deleteEntry(database, entry.get());
            if (deleted) {
                saveDatabase(database, DEFAULT_PASSWORD);
                log.info("成功删除密码条目：{}", title);
                return true;
            } else {
                log.warn("删除密码条目失败：{}", title);
                return false;
            }
        } else {
            log.warn("未找到要删除的密码条目：{}", title);
            return false;
        }
    }

    /**
     * 更新主密码
     *
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否更新成功
     * @throws IOException 数据库操作异常
     */
    public boolean updateMasterPassword(String oldPassword, String newPassword) throws IOException {
        log.info("开始更新主密码");

        if (oldPassword == null || oldPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("旧密码不能为空");
        }
        if (newPassword == null || newPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("新密码不能为空");
        }

        try {
            // 使用旧密码加载数据库
            JacksonDatabase database = loadDatabase(oldPassword);

            // 使用新密码保存数据库
            saveDatabase(database, newPassword);

            log.info("成功更新主密码");
            return true;
        } catch (IOException e) {
            log.error("更新主密码失败，可能是旧密码错误：{}", e.getMessage());
            throw new IOException("更新主密码失败，请检查旧密码是否正确", e);
        }
    }

    /**
     * 检查数据库是否已初始化
     *
     * @return 如果数据库文件存在且可读取返回true，否则返回false
     */
    public boolean isDatabaseInitialized() {
        try {
            String databasePath = getDatabaseFilePath();
            File file = new File(databasePath);

            // 检查文件是否存在且不为空
            if (!file.exists() || file.length() == 0) {
                return false;
            }

            // 尝试读取文件头部，验证是否为有效的KeePass文件
            // 这里简单检查文件是否存在，实际使用时可以尝试加载来验证
            return true;
        } catch (Exception e) {
            log.debug("检查数据库初始化状态时出错：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 初始化密码数据库
     * 创建新的KeePass数据库并设置主密码
     *
     * @param request 初始化请求，包含主密码等信息
     * @return 初始化成功返回true，否则返回false
     * @throws IOException 初始化过程中的IO异常
     * @throws IllegalArgumentException 参数验证失败
     */
    public boolean initializeDatabase(DatabaseInitRequest request) throws IOException {
        log.info("开始初始化密码数据库");

        // 参数验证
        if (request == null) {
            throw new IllegalArgumentException("初始化请求不能为空");
        }

        if (!request.isPasswordMatched()) {
            throw new IllegalArgumentException("密码确认不匹配");
        }

        String masterPassword = request.getMasterPassword();
        if (masterPassword == null || masterPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("主密码不能为空");
        }

        if (masterPassword.length() < 6) {
            throw new IllegalArgumentException("主密码长度不能少于6个字符");
        }

        // 检查数据库是否已存在
        if (isDatabaseInitialized() && !request.isForceReinit()) {
            throw new IllegalArgumentException("数据库已存在，如需重新初始化请设置强制重新初始化标志");
        }

        try {
            // 创建空白数据库
            JacksonDatabase database = keepPassManager.createEmptyDatabase();

            // 设置数据库基本信息
            database.setName(request.getDatabaseNameOrDefault());
            database.setDescription(request.getDescriptionOrDefault());

            // 保存数据库到文件
            String databasePath = getDatabaseFilePath();
            KdbxCreds credentials = new KdbxCreds(masterPassword.getBytes());
            keepPassManager.save(database, databasePath, credentials);

            log.info("成功初始化密码数据库，路径：{}", databasePath);
            return true;

        } catch (Exception e) {
            log.error("初始化密码数据库失败：{}", e.getMessage(), e);
            throw new IOException("初始化密码数据库失败：" + e.getMessage(), e);
        }
    }

    /**
     * 重置数据库
     * 删除现有数据库文件，为重新初始化做准备
     *
     * @return 重置成功返回true，否则返回false
     */
    public boolean resetDatabase() {
        try {
            String databasePath = getDatabaseFilePath();
            File file = new File(databasePath);

            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("成功删除数据库文件：{}", databasePath);
                } else {
                    log.warn("删除数据库文件失败：{}", databasePath);
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("重置数据库失败：{}", e.getMessage(), e);
            return false;
        }
    }
}
