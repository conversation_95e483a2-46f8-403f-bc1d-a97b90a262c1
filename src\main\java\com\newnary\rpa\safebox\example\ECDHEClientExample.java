package com.newnary.rpa.safebox.example;

import javax.crypto.KeyAgreement;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.*;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * ECDHE客户端示例
 * 这个类展示了如何使用ECDHE控制器API进行密钥交换
 */
public class ECDHEClientExample {

    private static final String SERVER_URL = "http://localhost:8080/api/ecdhe";
    private static final String ALGORITHM = "EC";
    private static final String CURVE_NAME = "secp256r1"; // NIST P-256 曲线
    private static final String KEY_AGREEMENT_ALGORITHM = "ECDH";
    
    private final String clientId;
    private KeyPair clientKeyPair;
    
    public ECDHEClientExample(String clientId) {
        this.clientId = clientId;
    }
    
    /**
     * 生成客户端密钥对
     */
    public void generateKeyPair() throws Exception {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(ALGORITHM);
        ECGenParameterSpec ecSpec = new ECGenParameterSpec(CURVE_NAME);
        keyPairGenerator.initialize(ecSpec, new SecureRandom());
        clientKeyPair = keyPairGenerator.generateKeyPair();
        System.out.println("客户端密钥对生成完成");
    }
    
    /**
     * 从服务器获取公钥
     * @return 服务器公钥（Base64编码）
     */
    public String getServerPublicKey() throws Exception {
        // 构建请求体
        String requestBody = String.format("{\"clientId\":\"%s\"}", clientId);
        
        // 创建HTTP连接
        URL url = new URL(SERVER_URL + "/generate-keypair");
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        
        // 发送请求
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.getBytes("utf-8");
            os.write(input, 0, input.length);
        }
        
        // 获取响应
        int statusCode = connection.getResponseCode();
        StringBuilder responseBody = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), "utf-8"))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                responseBody.append(responseLine.trim());
            }
        }
        
        // 解析响应
        if (statusCode != 200) {
            throw new RuntimeException("Failed to get server public key: " + responseBody.toString());
        }
        
        // 从响应中提取公钥
        String responseBodyStr = responseBody.toString();
        // 简单解析JSON（实际应用中应使用JSON库）
        String serverPublicKey = responseBodyStr.split("\"publicKey\":\"")[1].split("\"")[0];
        
        System.out.println("获取到服务器公钥: " + serverPublicKey);
        return serverPublicKey;
    }
    
    /**
     * 计算共享密钥
     * @param serverPublicKeyBase64 服务器公钥（Base64编码）
     * @return 共享密钥（Base64编码）
     */
    public String computeSharedSecret(String serverPublicKeyBase64) throws Exception {
        // 获取客户端公钥（Base64编码）
        String clientPublicKeyBase64 = Base64.getEncoder().encodeToString(clientKeyPair.getPublic().getEncoded());
        
        // 构建请求体
        String requestBody = String.format("{\"clientId\":\"%s\",\"clientPublicKey\":\"%s\"}", 
                clientId, clientPublicKeyBase64);
        
        // 创建HTTP连接
        URL url = new URL(SERVER_URL + "/compute-shared-secret");
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        
        // 发送请求
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = requestBody.getBytes("utf-8");
            os.write(input, 0, input.length);
        }
        
        // 获取响应
        int statusCode = connection.getResponseCode();
        StringBuilder responseBody = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), "utf-8"))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                responseBody.append(responseLine.trim());
            }
        }
        
        // 解析响应
        if (statusCode != 200) {
            throw new RuntimeException("Failed to compute shared secret: " + responseBody.toString());
        }
        
        // 从响应中提取服务器计算的共享密钥
        String responseBodyStr = responseBody.toString();
        // 简单解析JSON（实际应用中应使用JSON库）
        String serverSharedSecret = responseBodyStr.split("\"sharedSecret\":\"")[1].split("\"")[0];
        
        // 客户端也计算共享密钥（用于验证）
        byte[] serverPublicKeyBytes = Base64.getDecoder().decode(serverPublicKeyBase64);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(serverPublicKeyBytes);
        PublicKey serverPublicKey = keyFactory.generatePublic(keySpec);
        
        KeyAgreement keyAgreement = KeyAgreement.getInstance(KEY_AGREEMENT_ALGORITHM);
        keyAgreement.init(clientKeyPair.getPrivate());
        keyAgreement.doPhase(serverPublicKey, true);
        byte[] clientSharedSecret = keyAgreement.generateSecret();
        String clientSharedSecretBase64 = Base64.getEncoder().encodeToString(clientSharedSecret);
        
        // 验证客户端和服务器计算的共享密钥是否一致
        boolean keysMatch = serverSharedSecret.equals(clientSharedSecretBase64);
        System.out.println("客户端和服务器共享密钥是否一致: " + keysMatch);
        System.out.println("共享密钥: " + clientSharedSecretBase64);
        
        return clientSharedSecretBase64;
    }
    
    /**
     * 清除服务器上的密钥对
     */
    public void clearKeyPair() throws Exception {
        // 创建HTTP连接
        URL url = new URL(SERVER_URL + "/keypair/" + clientId);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("DELETE");
        
        // 获取响应
        int statusCode = connection.getResponseCode();
        StringBuilder responseBody = new StringBuilder();
        try (BufferedReader br = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), "utf-8"))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                responseBody.append(responseLine.trim());
            }
        }
        
        // 解析响应
        if (statusCode != 200) {
            throw new RuntimeException("Failed to clear key pair: " + responseBody.toString());
        }
        
        System.out.println("服务器密钥对已清除");
    }
    
    /**
     * 运行完整的ECDHE密钥交换流程
     */
    public void runFullExchange() {
        try {
            // 1. 生成客户端密钥对
            generateKeyPair();
            
            // 2. 从服务器获取公钥
            String serverPublicKey = getServerPublicKey();
            
            // 3. 计算共享密钥
            String sharedSecret = computeSharedSecret(serverPublicKey);
            
            // 4. 清除服务器上的密钥对
            clearKeyPair();
            
            System.out.println("ECDHE密钥交换流程完成！");
        } catch (Exception e) {
            System.err.println("ECDHE密钥交换流程失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 主方法，用于测试
     */
    public static void main(String[] args) {
        ECDHEClientExample client = new ECDHEClientExample("test-client-" + System.currentTimeMillis());
        client.runFullExchange();
    }
}