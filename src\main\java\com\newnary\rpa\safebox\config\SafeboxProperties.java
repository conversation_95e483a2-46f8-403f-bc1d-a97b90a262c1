package com.newnary.rpa.safebox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * Safebox配置属性类
 *
 * <AUTHOR>
 * @since Created on 2025-09-10
 */
@Data
@Component
@ConfigurationProperties(prefix = "safebox")
public class SafeboxProperties {

    /**
     * 应用ID列表
     */
    private Set<String> appIds;

    /**
     * 公钥
     **/
    private String ecPublicKey;

    /**
     * 私钥
     **/
    private String ecPrivateKey;

    /**
     * KeePass相关配置
     */
    private KeePass keepass;

    /**
     * KeePass配置内部类
     */
    @Data
    public static class KeePass {
        /**
         * KeePass数据库文件路径
         */
        private String kdbxPath;
    }
}