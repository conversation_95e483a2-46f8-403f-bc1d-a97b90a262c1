package com.newnary.rpa.safebox.controller.manager;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.newnary.rpa.safebox.dto.MasterPasswordUpdateRequest;
import com.newnary.rpa.safebox.dto.SecurityEntry;
import com.newnary.rpa.safebox.service.SecurityManagerService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SecurityManagerController单元测试
 *
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@WebMvcTest(SecurityManagerController.class)
class SecurityManagerControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SecurityManagerService securityManagerService;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试获取账号列表API
     */
    @Test
    void testGetEntries() throws Exception {
        // 准备测试数据
        SecurityEntry entry1 = new SecurityEntry();
        entry1.setTitle("Test Entry 1");
        entry1.setUsername("user1");
        entry1.setPassword("password1");

        SecurityEntry entry2 = new SecurityEntry();
        entry2.setTitle("Test Entry 2");
        entry2.setUsername("user2");
        entry2.setPassword("password2");

        List<SecurityEntry> entries = Arrays.asList(entry1, entry2);

        // Mock服务方法
        when(securityManagerService.getAllEntries()).thenReturn(entries);

        // 执行请求并验证结果
        mockMvc.perform(get("/security-manager/entries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("成功获取账号列表"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].title").value("Test Entry 1"))
                .andExpect(jsonPath("$.data[0].username").value("user1"))
                .andExpect(jsonPath("$.data[1].title").value("Test Entry 2"))
                .andExpect(jsonPath("$.data[1].username").value("user2"));
    }

    /**
     * 测试保存账密API
     */
    @Test
    void testSaveEntry() throws Exception {
        // 准备测试数据
        SecurityEntry entry = new SecurityEntry();
        entry.setTitle("New Entry");
        entry.setUsername("newuser");
        entry.setPassword("newpassword");

        // Mock服务方法
        when(securityManagerService.saveEntry(any(SecurityEntry.class))).thenReturn(true);

        // 执行请求并验证结果
        mockMvc.perform(post("/security-manager/entry")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(entry)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("成功保存账密"))
                .andExpect(jsonPath("$.data").value(true));
    }

    /**
     * 测试保存账密API - 参数校验失败
     */
    @Test
    void testSaveEntryWithInvalidParams() throws Exception {
        // 准备测试数据 - 缺少标题
        SecurityEntry entry = new SecurityEntry();
        entry.setUsername("newuser");
        entry.setPassword("newpassword");

        // 执行请求并验证结果
        mockMvc.perform(post("/security-manager/entry")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(entry)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("FAIL"))
                .andExpect(jsonPath("$.message").value("条目标题不能为空"));
    }

    /**
     * 测试删除账密API
     */
    @Test
    void testDeleteEntry() throws Exception {
        // Mock服务方法
        when(securityManagerService.deleteEntry(anyString())).thenReturn(true);

        // 执行请求并验证结果
        mockMvc.perform(delete("/security-manager/entry/Test Entry"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("成功删除账密"))
                .andExpect(jsonPath("$.data").value(true));
    }

    /**
     * 测试更新主密码API
     */
    @Test
    void testUpdateMasterPassword() throws Exception {
        // 准备测试数据
        MasterPasswordUpdateRequest request = new MasterPasswordUpdateRequest();
        request.setOldPassword("oldpassword");
        request.setNewPassword("newpassword");

        // Mock服务方法
        when(securityManagerService.updateMasterPassword(anyString(), anyString())).thenReturn(true);

        // 执行请求并验证结果
        mockMvc.perform(put("/security-manager/master-password")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("成功更新主密码"))
                .andExpect(jsonPath("$.data").value(true));
    }
}
