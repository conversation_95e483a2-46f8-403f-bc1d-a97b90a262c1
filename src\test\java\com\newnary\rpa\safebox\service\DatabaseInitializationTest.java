package com.newnary.rpa.safebox.service;

import com.newnary.rpa.safebox.dto.DatabaseInitRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据库初始化功能测试
 *
 * <AUTHOR>
 * @since Created on 2025-09-11
 */
@SpringBootTest
@TestPropertySource(properties = {
    "safebox.keepass.kdbx-path=test-database"
})
class DatabaseInitializationTest {

    @Resource
    private SecurityManagerService securityManagerService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // 每个测试前清理可能存在的测试数据库
        try {
            securityManagerService.resetDatabase();
        } catch (Exception e) {
            // 忽略重置错误，可能数据库本来就不存在
        }
    }

    @Test
    void testIsDatabaseInitialized_WhenNotInitialized() {
        // 测试数据库未初始化状态
        boolean isInitialized = securityManagerService.isDatabaseInitialized();
        assertFalse(isInitialized, "新环境下数据库应该是未初始化状态");
    }

    @Test
    void testInitializeDatabase_Success() throws IOException {
        // 准备初始化请求
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("testPassword123");
        request.setConfirmPassword("testPassword123");
        request.setDatabaseName("测试密码库");
        request.setDescription("这是一个测试用的密码库");

        // 执行初始化
        boolean result = securityManagerService.initializeDatabase(request);
        assertTrue(result, "数据库初始化应该成功");

        // 验证初始化状态
        boolean isInitialized = securityManagerService.isDatabaseInitialized();
        assertTrue(isInitialized, "初始化后数据库状态应该为已初始化");
    }

    @Test
    void testInitializeDatabase_PasswordMismatch() {
        // 准备密码不匹配的请求
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("password123");
        request.setConfirmPassword("differentPassword");

        // 执行初始化，应该抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> securityManagerService.initializeDatabase(request),
            "密码不匹配应该抛出异常"
        );

        assertEquals("密码确认不匹配", exception.getMessage());
    }

    @Test
    void testInitializeDatabase_EmptyPassword() {
        // 准备空密码的请求
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("");
        request.setConfirmPassword("");

        // 执行初始化，应该抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> securityManagerService.initializeDatabase(request),
            "空密码应该抛出异常"
        );

        assertEquals("主密码不能为空", exception.getMessage());
    }

    @Test
    void testInitializeDatabase_ShortPassword() {
        // 准备过短密码的请求
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("123");
        request.setConfirmPassword("123");

        // 执行初始化，应该抛出异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> securityManagerService.initializeDatabase(request),
            "过短密码应该抛出异常"
        );

        assertEquals("主密码长度不能少于6个字符", exception.getMessage());
    }

    @Test
    void testInitializeDatabase_AlreadyInitialized() throws IOException {
        // 先初始化数据库
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("testPassword123");
        request.setConfirmPassword("testPassword123");
        securityManagerService.initializeDatabase(request);

        // 再次尝试初始化，应该抛出异常
        DatabaseInitRequest secondRequest = new DatabaseInitRequest();
        secondRequest.setMasterPassword("newPassword123");
        secondRequest.setConfirmPassword("newPassword123");

        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> securityManagerService.initializeDatabase(secondRequest),
            "重复初始化应该抛出异常"
        );

        assertTrue(exception.getMessage().contains("数据库已存在"));
    }

    @Test
    void testInitializeDatabase_ForceReinit() throws IOException {
        // 先初始化数据库
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("testPassword123");
        request.setConfirmPassword("testPassword123");
        securityManagerService.initializeDatabase(request);

        // 强制重新初始化
        DatabaseInitRequest reinitRequest = new DatabaseInitRequest();
        reinitRequest.setMasterPassword("newPassword123");
        reinitRequest.setConfirmPassword("newPassword123");
        reinitRequest.setForceReinit(true);

        boolean result = securityManagerService.initializeDatabase(reinitRequest);
        assertTrue(result, "强制重新初始化应该成功");
    }

    @Test
    void testResetDatabase() throws IOException {
        // 先初始化数据库
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("testPassword123");
        request.setConfirmPassword("testPassword123");
        securityManagerService.initializeDatabase(request);

        // 验证数据库已初始化
        assertTrue(securityManagerService.isDatabaseInitialized());

        // 重置数据库
        boolean resetResult = securityManagerService.resetDatabase();
        assertTrue(resetResult, "数据库重置应该成功");

        // 验证数据库已重置
        assertFalse(securityManagerService.isDatabaseInitialized(), "重置后数据库应该是未初始化状态");
    }

    @Test
    void testDatabaseInitRequest_PasswordMatched() {
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("password123");
        request.setConfirmPassword("password123");

        assertTrue(request.isPasswordMatched(), "相同密码应该匹配");
    }

    @Test
    void testDatabaseInitRequest_PasswordNotMatched() {
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setMasterPassword("password123");
        request.setConfirmPassword("differentPassword");

        assertFalse(request.isPasswordMatched(), "不同密码应该不匹配");
    }

    @Test
    void testDatabaseInitRequest_DefaultValues() {
        DatabaseInitRequest request = new DatabaseInitRequest();

        assertEquals("SafeBox密码库", request.getDatabaseNameOrDefault());
        assertEquals("由SafeBox系统创建的安全密码库", request.getDescriptionOrDefault());
    }

    @Test
    void testDatabaseInitRequest_CustomValues() {
        DatabaseInitRequest request = new DatabaseInitRequest();
        request.setDatabaseName("我的密码库");
        request.setDescription("个人使用的密码库");

        assertEquals("我的密码库", request.getDatabaseNameOrDefault());
        assertEquals("个人使用的密码库", request.getDescriptionOrDefault());
    }
}
