package com.newnary.rpa.safebox.controller.web;

import com.newnary.rpa.safebox.dto.DatabaseInitRequest;
import com.newnary.rpa.safebox.dto.SecurityEntry;
import com.newnary.rpa.safebox.service.SecurityManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Web页面控制器
 * 提供基于Thymeleaf的Web界面
 *
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@Controller
@RequestMapping("/web")
@Slf4j
public class WebController {

    @Resource
    private SecurityManagerService securityManagerService;

    /**
     * 首页 - 显示所有密码条目
     *
     * @param model 模型对象
     * @return 首页模板
     */
    @GetMapping({"/", "/index"})
    public String index(Model model) {
        try {
            log.info("访问首页");

            // 检查数据库是否已初始化
            if (!securityManagerService.isDatabaseInitialized()) {
                log.info("数据库未初始化，重定向到初始化页面");
                return "redirect:/web/init";
            }

            List<SecurityEntry> entries = securityManagerService.getAllEntries();
            model.addAttribute("entries", entries);
            model.addAttribute("totalCount", entries.size());

            // 统计信息
            long uniqueWebsites = entries.stream()
                .map(SecurityEntry::getTitle)
                .distinct()
                .count();
            model.addAttribute("websiteCount", uniqueWebsites);

            return "index";
        } catch (Exception e) {
            log.error("获取密码条目失败：{}", e.getMessage(), e);

            // 如果是数据库相关错误，可能需要重新初始化
            if (e.getMessage().contains("密码错误") || e.getMessage().contains("文件损坏")) {
                model.addAttribute("error", "数据库访问失败，可能需要重新初始化：" + e.getMessage());
                model.addAttribute("showReinitButton", true);
            } else {
                model.addAttribute("error", "获取密码条目失败：" + e.getMessage());
            }

            model.addAttribute("entries", new ArrayList<>());
            model.addAttribute("totalCount", 0);
            model.addAttribute("websiteCount", 0);
            return "index";
        }
    }

    /**
     * 添加密码页面
     *
     * @param model 模型对象
     * @return 添加密码模板
     */
    @GetMapping("/add")
    public String addPage(Model model) {
        log.info("访问添加密码页面");
        model.addAttribute("entry", new SecurityEntry());
        model.addAttribute("isEdit", false);
        return "add-edit";
    }

    /**
     * 编辑密码页面
     *
     * @param title 密码条目标题
     * @param model 模型对象
     * @return 编辑密码模板
     */
    @GetMapping("/edit/{title}")
    public String editPage(@PathVariable String title, Model model) {
        try {
            log.info("访问编辑密码页面，标题：{}", title);
            List<SecurityEntry> entries = securityManagerService.getAllEntries();
            SecurityEntry entry = entries.stream()
                    .filter(e -> title.equals(e.getTitle()))
                    .findFirst()
                    .orElse(null);
            
            if (entry == null) {
                model.addAttribute("error", "未找到指定的密码条目");
                return "redirect:/web/";
            }
            
            model.addAttribute("entry", entry);
            model.addAttribute("isEdit", true);
            return "add-edit";
        } catch (Exception e) {
            log.error("获取密码条目失败：{}", e.getMessage(), e);
            model.addAttribute("error", "获取密码条目失败：" + e.getMessage());
            return "redirect:/web/";
        }
    }

    /**
     * 保存密码条目
     *
     * @param entry 密码条目
     * @param redirectAttributes 重定向属性
     * @return 重定向到首页
     */
    @PostMapping("/save")
    public String save(@ModelAttribute SecurityEntry entry, RedirectAttributes redirectAttributes) {
        try {
            log.info("保存密码条目，标题：{}", entry.getTitle());
            
            // 参数校验
            if (entry.getTitle() == null || entry.getTitle().trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "条目标题不能为空");
                return "redirect:/web/add";
            }
            if (entry.getUsername() == null || entry.getUsername().trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "用户名不能为空");
                return "redirect:/web/add";
            }
            if (entry.getPassword() == null || entry.getPassword().trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "密码不能为空");
                return "redirect:/web/add";
            }
            
            boolean result = securityManagerService.saveEntry(entry);
            if (result) {
                redirectAttributes.addFlashAttribute("success", "密码条目保存成功");
            } else {
                redirectAttributes.addFlashAttribute("error", "密码条目保存失败");
            }
        } catch (Exception e) {
            log.error("保存密码条目失败：{}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "保存密码条目失败：" + e.getMessage());
        }
        
        return "redirect:/web/";
    }

    /**
     * 删除密码条目
     *
     * @param title 密码条目标题
     * @param redirectAttributes 重定向属性
     * @return 重定向到首页
     */
    @PostMapping("/delete/{title}")
    public String delete(@PathVariable String title, RedirectAttributes redirectAttributes) {
        try {
            log.info("删除密码条目，标题：{}", title);
            boolean result = securityManagerService.deleteEntry(title);
            if (result) {
                redirectAttributes.addFlashAttribute("success", "密码条目删除成功");
            } else {
                redirectAttributes.addFlashAttribute("error", "密码条目删除失败，可能条目不存在");
            }
        } catch (Exception e) {
            log.error("删除密码条目失败：{}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "删除密码条目失败：" + e.getMessage());
        }
        
        return "redirect:/web/";
    }

    /**
     * 主密码管理页面
     *
     * @return 主密码管理模板
     */
    @GetMapping("/master-password")
    public String masterPasswordPage() {
        log.info("访问主密码管理页面");
        return "master-password";
    }

    /**
     * 更新主密码
     *
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @param confirmPassword 确认密码
     * @param redirectAttributes 重定向属性
     * @return 重定向到主密码管理页面
     */
    @PostMapping("/master-password")
    public String updateMasterPassword(@RequestParam String oldPassword,
                                     @RequestParam String newPassword,
                                     @RequestParam String confirmPassword,
                                     RedirectAttributes redirectAttributes) {
        try {
            log.info("更新主密码");
            
            // 参数校验
            if (oldPassword == null || oldPassword.trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "旧密码不能为空");
                return "redirect:/web/master-password";
            }
            if (newPassword == null || newPassword.trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "新密码不能为空");
                return "redirect:/web/master-password";
            }
            if (!newPassword.equals(confirmPassword)) {
                redirectAttributes.addFlashAttribute("error", "新密码和确认密码不匹配");
                return "redirect:/web/master-password";
            }
            if (oldPassword.equals(newPassword)) {
                redirectAttributes.addFlashAttribute("error", "新密码不能与旧密码相同");
                return "redirect:/web/master-password";
            }
            
            boolean result = securityManagerService.updateMasterPassword(oldPassword, newPassword);
            if (result) {
                redirectAttributes.addFlashAttribute("success", "主密码更新成功");
            } else {
                redirectAttributes.addFlashAttribute("error", "主密码更新失败");
            }
        } catch (Exception e) {
            log.error("更新主密码失败：{}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "更新主密码失败：" + e.getMessage());
        }
        
        return "redirect:/web/master-password";
    }

    /**
     * 数据库初始化页面
     * 检查数据库状态，如果未初始化则显示初始化页面
     *
     * @param model 模型对象
     * @return 初始化页面模板或重定向到首页
     */
    @GetMapping("/init")
    public String initPage(Model model) {
        try {
            log.info("访问数据库初始化页面");

            // 检查数据库是否已初始化
            boolean isInitialized = securityManagerService.isDatabaseInitialized();
            if (isInitialized) {
                log.info("数据库已初始化，重定向到首页");
                return "redirect:/web/";
            }

            model.addAttribute("request", new DatabaseInitRequest());
            return "init";
        } catch (Exception e) {
            log.error("访问初始化页面失败：{}", e.getMessage(), e);
            model.addAttribute("error", "访问初始化页面失败：" + e.getMessage());
            model.addAttribute("request", new DatabaseInitRequest());
            return "init";
        }
    }

    /**
     * 执行数据库初始化
     *
     * @param request 初始化请求
     * @param redirectAttributes 重定向属性
     * @return 重定向到首页或初始化页面
     */
    @PostMapping("/init")
    public String initializeDatabase(@ModelAttribute DatabaseInitRequest request,
                                   RedirectAttributes redirectAttributes) {
        try {
            log.info("执行数据库初始化");

            // 参数校验
            if (request.getMasterPassword() == null || request.getMasterPassword().trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "主密码不能为空");
                return "redirect:/web/init";
            }
            if (request.getConfirmPassword() == null || request.getConfirmPassword().trim().isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "确认密码不能为空");
                return "redirect:/web/init";
            }
            if (!request.isPasswordMatched()) {
                redirectAttributes.addFlashAttribute("error", "密码确认不匹配");
                return "redirect:/web/init";
            }
            if (request.getMasterPassword().length() < 6) {
                redirectAttributes.addFlashAttribute("error", "主密码长度不能少于6个字符");
                return "redirect:/web/init";
            }

            boolean result = securityManagerService.initializeDatabase(request);
            if (result) {
                redirectAttributes.addFlashAttribute("success", "密码数据库初始化成功！欢迎使用SafeBox");
                return "redirect:/web/";
            } else {
                redirectAttributes.addFlashAttribute("error", "数据库初始化失败");
                return "redirect:/web/init";
            }
        } catch (IllegalArgumentException e) {
            log.warn("初始化数据库参数错误：{}", e.getMessage());
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/web/init";
        } catch (Exception e) {
            log.error("初始化数据库失败：{}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "初始化数据库失败：" + e.getMessage());
            return "redirect:/web/init";
        }
    }

    /**
     * 数据库重置页面
     *
     * @return 重置确认页面模板
     */
    @GetMapping("/reset")
    public String resetPage() {
        log.info("访问数据库重置页面");
        return "reset";
    }

    /**
     * 执行数据库重置
     *
     * @param confirmation 确认字符串
     * @param redirectAttributes 重定向属性
     * @return 重定向到初始化页面或重置页面
     */
    @PostMapping("/reset")
    public String resetDatabase(@RequestParam String confirmation,
                              RedirectAttributes redirectAttributes) {
        try {
            log.info("执行数据库重置");

            // 确认字符串验证
            if (!"RESET".equals(confirmation)) {
                redirectAttributes.addFlashAttribute("error", "请输入正确的确认字符串 'RESET'");
                return "redirect:/web/reset";
            }

            boolean result = securityManagerService.resetDatabase();
            if (result) {
                redirectAttributes.addFlashAttribute("success", "数据库重置成功，请重新初始化");
                return "redirect:/web/init";
            } else {
                redirectAttributes.addFlashAttribute("error", "数据库重置失败");
                return "redirect:/web/reset";
            }
        } catch (Exception e) {
            log.error("重置数据库失败：{}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "重置数据库失败：" + e.getMessage());
            return "redirect:/web/reset";
        }
    }
}
