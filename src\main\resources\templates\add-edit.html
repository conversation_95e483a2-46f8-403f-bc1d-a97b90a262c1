<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${isEdit ? '编辑密码 - SafeBox' : '添加密码 - SafeBox'}">添加密码 - SafeBox</title>

    <!-- CSS样式 -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <!-- 网站图标 -->
    <link rel="icon" type="image/x-icon" th:href="@{/favicon.ico}">
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <h1>🔐 SafeBox</h1>
            <p class="subtitle">安全密码管理系统</p>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <ul>
                <li><a th:href="@{/web/}">🏠 首页</a></li>
                <li><a th:href="@{/web/add}" class="active">➕ 添加密码</a></li>
                <li><a th:href="@{/web/master-password}">🔑 主密码管理</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container">
        <!-- 消息提示区域 -->
        <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
        <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>
        <div th:if="${info}" class="alert alert-info" th:text="${info}"></div>

        <!-- 页面内容 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title" th:text="${isEdit ? '✏️ 编辑密码条目' : '➕ 添加密码条目'}">添加密码条目</h2>
            </div>

            <form th:action="@{/web/save}" method="post" th:object="${entry}">
                <div class="form-group">
                    <label for="title" class="form-label">标题 *</label>
                    <input type="text" id="title" name="title" class="form-control" 
                           th:field="*{title}" placeholder="请输入密码条目标题" required
                           th:readonly="${isEdit}">
                    <small style="color: #666; font-size: 12px;">
                        <span th:if="${isEdit}">编辑模式下标题不可修改</span>
                        <span th:unless="${isEdit}">用于识别此密码条目的唯一标题</span>
                    </small>
                </div>

                <div class="form-group">
                    <label for="username" class="form-label">用户名 *</label>
                    <input type="text" id="username" name="username" class="form-control" 
                           th:field="*{username}" placeholder="请输入用户名或邮箱" required>
                    <small style="color: #666; font-size: 12px;">登录时使用的用户名或邮箱地址</small>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">密码 *</label>
                    <div class="password-field">
                        <input type="password" id="password" name="password" class="form-control" 
                               th:field="*{password}" placeholder="请输入密码" required>
                        <button type="button" class="password-toggle">显示</button>
                    </div>
                    <div style="margin-top: 10px; display: flex; gap: 10px; flex-wrap: wrap;">
                        <button type="button" class="btn btn-secondary btn-sm" onclick="generateRandomPassword()">
                            🎲 生成随机密码
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="checkPasswordStrengthUI()">
                            🔍 检查密码强度
                        </button>
                    </div>
                    <div id="password-strength" style="margin-top: 10px; display: none;">
                        <div style="font-size: 12px; margin-bottom: 5px;">密码强度：<span id="strength-level"></span></div>
                        <div id="strength-bar" style="height: 4px; background: #e1e5e9; border-radius: 2px; overflow: hidden;">
                            <div id="strength-fill" style="height: 100%; transition: all 0.3s ease;"></div>
                        </div>
                        <div id="strength-feedback" style="font-size: 11px; color: #666; margin-top: 5px;"></div>
                    </div>
                </div>

                <div class="action-buttons">
                    <a th:href="@{/web/}" class="btn btn-secondary">❌ 取消</a>
                    <button type="submit" class="btn btn-primary">
                        <span th:if="${isEdit}">💾 更新密码</span>
                        <span th:unless="${isEdit}">➕ 添加密码</span>
                    </button>
                </div>
            </form>
        </div>

        <!-- 密码生成器设置 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🎲 密码生成器设置</h3>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div class="form-group">
                    <label for="password-length" class="form-label">密码长度</label>
                    <input type="range" id="password-length" min="8" max="32" value="16" 
                           style="width: 100%;" oninput="updateLengthDisplay(this.value)">
                    <div style="text-align: center; font-size: 14px; margin-top: 5px;">
                        <span id="length-display">16</span> 个字符
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">包含字符类型</label>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" id="include-uppercase" checked style="margin-right: 8px;">
                            大写字母 (A-Z)
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" id="include-lowercase" checked style="margin-right: 8px;">
                            小写字母 (a-z)
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" id="include-numbers" checked style="margin-right: 8px;">
                            数字 (0-9)
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="checkbox" id="include-symbols" checked style="margin-right: 8px;">
                            特殊字符 (!@#$%^&*)
                        </label>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button type="button" class="btn btn-primary" onclick="generateCustomPassword()">
                    🎲 生成自定义密码
                </button>
            </div>
        </div>

        <!-- 使用提示 -->
        <div class="card" style="background: #f8f9fa;">
            <div class="card-header">
                <h3 class="card-title">💡 使用提示</h3>
            </div>
            <ul style="margin: 0; padding-left: 20px;">
                <li>标题用于唯一标识密码条目，建议使用网站名称或应用名称</li>
                <li>用户名可以是邮箱地址、手机号或其他登录标识</li>
                <li>建议使用强密码，包含大小写字母、数字和特殊字符</li>
                <li>可以使用密码生成器创建安全的随机密码</li>
                <li>所有数据都会经过 KeePass 标准加密后存储</li>
            </ul>
        </div>
    </main>

    <!-- 页面底部 -->
    <footer style="text-align: center; padding: 40px 0; color: #666; border-top: 1px solid #e1e5e9; margin-top: 50px;">
        <div class="container">
            <p>&copy; 2025 SafeBox. 基于 Spring Boot + Thymeleaf + KeePass 构建</p>
            <p style="font-size: 0.9em; margin-top: 10px;">
                <span>🔒 安全</span> |
                <span>🚀 高效</span> |
                <span>💡 智能</span>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script th:src="@{/js/app.js}"></script>

    <script>
        // 更新长度显示
        function updateLengthDisplay(length) {
            document.getElementById('length-display').textContent = length;
        }

        // 生成随机密码（简单版本）
        function generateRandomPassword() {
            const password = generatePassword(16);
            document.getElementById('password').value = password;
            checkPasswordStrengthUI();
            showToast('已生成随机密码', 'success');
        }

        // 生成自定义密码
        function generateCustomPassword() {
            const length = parseInt(document.getElementById('password-length').value);
            const includeUppercase = document.getElementById('include-uppercase').checked;
            const includeLowercase = document.getElementById('include-lowercase').checked;
            const includeNumbers = document.getElementById('include-numbers').checked;
            const includeSymbols = document.getElementById('include-symbols').checked;

            let charset = '';
            if (includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            if (includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
            if (includeNumbers) charset += '0123456789';
            if (includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

            if (charset === '') {
                showToast('请至少选择一种字符类型', 'error');
                return;
            }

            let password = '';
            for (let i = 0; i < length; i++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }

            document.getElementById('password').value = password;
            checkPasswordStrengthUI();
            showToast('已生成自定义密码', 'success');
        }

        // 检查密码强度UI
        function checkPasswordStrengthUI() {
            const password = document.getElementById('password').value;
            if (!password) {
                document.getElementById('password-strength').style.display = 'none';
                return;
            }

            const strength = checkPasswordStrength(password);
            const strengthDiv = document.getElementById('password-strength');
            const levelSpan = document.getElementById('strength-level');
            const fillDiv = document.getElementById('strength-fill');
            const feedbackDiv = document.getElementById('strength-feedback');

            strengthDiv.style.display = 'block';
            levelSpan.textContent = strength.level;
            levelSpan.style.color = strength.color;
            fillDiv.style.width = (strength.score * 20) + '%';
            fillDiv.style.backgroundColor = strength.color;

            if (strength.feedback.length > 0) {
                feedbackDiv.textContent = '建议：' + strength.feedback.join('、');
            } else {
                feedbackDiv.textContent = '密码强度良好！';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 监听密码输入变化
            const passwordInput = document.getElementById('password');
            passwordInput.addEventListener('input', debounce(checkPasswordStrengthUI, 300));

            // 如果是编辑模式，检查现有密码强度
            if ([[${isEdit}]] && passwordInput.value) {
                checkPasswordStrengthUI();
            }
        });
    </script>
</body>
</html>
