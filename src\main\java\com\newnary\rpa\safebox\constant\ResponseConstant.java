package com.newnary.rpa.safebox.constant;

/**
 * 响应常量类
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
public class ResponseConstant {

    /**
     * 响应状态
     */
    public static class State {
        /**
         * 成功
         */
        public static final String SUCCESS = "SUCCESS";
        
        /**
         * 失败
         */
        public static final String FAIL = "FAIL";
    }
    

    /**
     * 响应消息
     */
    public static class Message {
        /**
         * 操作成功
         */
        public static final String SUCCESS = "操作成功";
        
        /**
         * 操作失败
         */
        public static final String FAIL = "操作失败";
        
        /**
         * 参数错误
         */
        public static final String PARAM_ERROR = "参数错误";
        
        /**
         * 未授权
         */
        public static final String UNAUTHORIZED = "未授权，请先登录";
        
        /**
         * 禁止访问
         */
        public static final String FORBIDDEN = "无权限访问";
        
        /**
         * 资源不存在
         */
        public static final String NOT_FOUND = "请求的资源不存在";
        
        /**
         * 服务器内部错误
         */
        public static final String SERVER_ERROR = "服务器内部错误";
    }
}