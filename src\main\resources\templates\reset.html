<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码库 - SafeBox</title>
    
    <!-- CSS样式 -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    
    <!-- 网站图标 -->
    <link rel="icon" type="image/x-icon" th:href="@{/favicon.ico}">
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <h1>🔐 SafeBox</h1>
            <p class="subtitle">安全密码管理系统 - 重置密码库</p>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container">
        <!-- 消息提示区域 -->
        <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
        <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>
        <div th:if="${info}" class="alert alert-info" th:text="${info}"></div>

        <!-- 导航链接 -->
        <div style="margin-bottom: 30px;">
            <a th:href="@{/web/}" class="btn btn-secondary">← 返回首页</a>
        </div>

        <!-- 警告信息 -->
        <div class="card" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; margin-bottom: 30px;">
            <div style="text-align: center;">
                <div style="font-size: 4em; margin-bottom: 20px;">⚠️</div>
                <h2 style="color: white; margin-bottom: 15px;">危险操作警告</h2>
                <p style="font-size: 1.1em; opacity: 0.9; margin-bottom: 0;">
                    您即将重置整个密码库！<br>
                    此操作将<strong>永久删除</strong>所有存储的密码数据，且<strong>无法恢复</strong>！
                </p>
            </div>
        </div>

        <!-- 重置表单 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">🗑️ 重置密码库</h2>
                <p style="color: #666; margin-top: 10px;">
                    请仔细阅读以下信息，确认您真的要执行此操作
                </p>
            </div>

            <!-- 重置说明 -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                <h3 style="color: #495057; margin-bottom: 15px;">📋 重置操作说明</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div>
                        <h4 style="color: #dc3545; margin-bottom: 10px;">❌ 将会删除的数据</h4>
                        <ul style="color: #666; line-height: 1.6;">
                            <li>所有保存的密码条目</li>
                            <li>用户名和网站信息</li>
                            <li>密码库的所有配置</li>
                            <li>主密码设置</li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="color: #28a745; margin-bottom: 10px;">✅ 重置后的状态</h4>
                        <ul style="color: #666; line-height: 1.6;">
                            <li>密码库文件将被删除</li>
                            <li>系统回到初始化状态</li>
                            <li>需要重新设置主密码</li>
                            <li>可以重新开始使用</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 确认表单 -->
            <form th:action="@{/web/reset}" method="post" onsubmit="return confirmReset()">
                <div class="form-group">
                    <label for="confirmation" class="form-label">
                        确认重置 *
                    </label>
                    <input type="text" id="confirmation" name="confirmation" class="form-control" 
                           placeholder="请输入 'RESET' 来确认重置操作" required
                           style="font-family: monospace; font-size: 16px; text-align: center; letter-spacing: 2px;">
                    <small style="color: #666; font-size: 12px;">
                        请准确输入大写字母 <strong>RESET</strong> 来确认此危险操作
                    </small>
                </div>

                <div class="form-group">
                    <label style="display: flex; align-items: center; font-weight: normal; color: #495057;">
                        <input type="checkbox" id="understand" required style="margin-right: 10px; transform: scale(1.2);">
                        我理解此操作将永久删除所有数据且无法恢复
                    </label>
                </div>

                <div class="form-group">
                    <label style="display: flex; align-items: center; font-weight: normal; color: #495057;">
                        <input type="checkbox" id="backup" required style="margin-right: 10px; transform: scale(1.2);">
                        我已经备份了重要的密码数据（如果需要）
                    </label>
                </div>

                <div class="action-buttons" style="margin-top: 30px;">
                    <button type="button" class="btn btn-secondary" onclick="window.location.href='/web/'">
                        取消操作
                    </button>
                    <button type="submit" class="btn btn-danger" style="font-size: 16px; padding: 15px 30px;">
                        🗑️ 确认重置密码库
                    </button>
                </div>
            </form>
        </div>

        <!-- 替代方案 -->
        <div class="card" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white;">
            <div class="card-header" style="border-color: rgba(255,255,255,0.2);">
                <h3 class="card-title" style="color: white;">💡 替代方案</h3>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: white; margin-bottom: 10px;">🔑 忘记主密码？</h4>
                    <p style="opacity: 0.9; line-height: 1.6;">
                        如果您只是忘记了主密码，可以尝试：<br>
                        • 回忆可能使用的密码组合<br>
                        • 检查是否有密码备份<br>
                        • 联系系统管理员
                    </p>
                </div>
                <div>
                    <h4 style="color: white; margin-bottom: 10px;">🔄 需要清理？</h4>
                    <p style="opacity: 0.9; line-height: 1.6;">
                        如果您只是想清理数据：<br>
                        • 可以逐个删除不需要的条目<br>
                        • 使用导出功能备份重要数据<br>
                        • 考虑更改主密码而非重置
                    </p>
                </div>
            </div>
        </div>
    </main>

    <!-- 页面底部 -->
    <footer style="text-align: center; padding: 40px 0; color: #666; border-top: 1px solid #e1e5e9; margin-top: 50px;">
        <div class="container">
            <p>&copy; 2025 SafeBox. 基于 Spring Boot + Thymeleaf + KeePass 构建</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script th:src="@{/js/app.js}"></script>
    
    <script>
        // 确认重置操作
        function confirmReset() {
            const confirmation = document.getElementById('confirmation').value;
            const understand = document.getElementById('understand').checked;
            const backup = document.getElementById('backup').checked;

            if (confirmation !== 'RESET') {
                showToast('请输入正确的确认字符串 "RESET"', 'error');
                return false;
            }

            if (!understand || !backup) {
                showToast('请确认您已理解操作后果并完成必要的备份', 'error');
                return false;
            }

            // 最终确认对话框
            const finalConfirm = confirm(
                '最后确认：您真的要重置密码库吗？\n\n' +
                '此操作将：\n' +
                '• 永久删除所有密码数据\n' +
                '• 无法恢复任何信息\n' +
                '• 需要重新初始化系统\n\n' +
                '点击"确定"继续，点击"取消"停止操作。'
            );

            if (finalConfirm) {
                showToast('正在执行重置操作...', 'info');
                return true;
            } else {
                showToast('重置操作已取消', 'info');
                return false;
            }
        }

        // 监听确认输入
        document.addEventListener('DOMContentLoaded', function() {
            const confirmationInput = document.getElementById('confirmation');
            const submitButton = document.querySelector('button[type="submit"]');

            confirmationInput.addEventListener('input', function() {
                if (this.value === 'RESET') {
                    this.style.borderColor = '#28a745';
                    this.style.backgroundColor = '#f8fff9';
                } else if (this.value.length > 0) {
                    this.style.borderColor = '#dc3545';
                    this.style.backgroundColor = '#fff5f5';
                } else {
                    this.style.borderColor = '#ced4da';
                    this.style.backgroundColor = '#fff';
                }
            });

            // 显示警告信息
            setTimeout(() => {
                showToast('⚠️ 警告：重置操作将永久删除所有数据！', 'error');
            }, 1000);
        });
    </script>
</body>
</html>
