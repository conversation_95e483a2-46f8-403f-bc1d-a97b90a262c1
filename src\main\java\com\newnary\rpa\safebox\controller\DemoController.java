package com.newnary.rpa.safebox.controller;

import com.newnary.rpa.safebox.constant.ResponseConstant;
import com.newnary.rpa.safebox.dto.BaseResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 示例控制器
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@RestController
@RequestMapping("/api/demo")
public class DemoController {

    /**
     * 成功响应示例
     * @return 成功响应
     */
    @GetMapping("/success")
    public BaseResponse<Map<String, Object>> success() {
        Map<String, Object> data = new HashMap<>();
        data.put("id", 1);
        data.put("name", "测试数据");
        return BaseResponse.success(data);
    }

    /**
     * 自定义成功消息响应示例
     * @param message 自定义消息
     * @return 成功响应
     */
    @GetMapping("/success-message")
    public BaseResponse<Map<String, Object>> successWithMessage(
            @RequestParam(defaultValue = "自定义成功消息") String message) {
        Map<String, Object> data = new HashMap<>();
        data.put("id", 2);
        data.put("name", "测试数据");
        return BaseResponse.success(message, data);
    }

    /**
     * 失败响应示例
     * @return 失败响应
     */
    @GetMapping("/fail")
    public BaseResponse<Void> fail() {
        return BaseResponse.fail("操作失败，请稍后重试");
    }

    /**
     * 自定义错误消息响应示例
     * @param type 错误类型
     * @return 失败响应
     */
    @GetMapping("/fail-message")
    public BaseResponse<Void> failWithMessage(
            @RequestParam(defaultValue = "param") String type) {
        String message;
        switch (type) {
            case "param":
                message = ResponseConstant.Message.PARAM_ERROR;
                break;
            case "auth":
                message = ResponseConstant.Message.UNAUTHORIZED;
                break;
            case "perm":
                message = ResponseConstant.Message.FORBIDDEN;
                break;
            case "notfound":
                message = ResponseConstant.Message.NOT_FOUND;
                break;
            case "business":
                message = "业务处理异常";
                break;
            default:
                message = ResponseConstant.Message.SERVER_ERROR;
        }
        return BaseResponse.fail(message);
    }

    /**
     * 自定义响应示例
     * @return 自定义响应
     */
    @GetMapping("/custom")
    public BaseResponse<String> custom() {
        return BaseResponse.custom(
                "CUSTOM",
                "这是一个自定义状态的响应",
                "自定义数据内容");
    }
}