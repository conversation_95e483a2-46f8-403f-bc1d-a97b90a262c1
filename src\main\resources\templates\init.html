<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化密码库 - SafeBox</title>
    
    <!-- CSS样式 -->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    
    <!-- 网站图标 -->
    <link rel="icon" type="image/x-icon" th:href="@{/favicon.ico}">
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <h1>🔐 SafeBox</h1>
            <p class="subtitle">安全密码管理系统 - 初始化</p>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container">
        <!-- 消息提示区域 -->
        <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
        <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>
        <div th:if="${info}" class="alert alert-info" th:text="${info}"></div>

        <!-- 欢迎信息 -->
        <div class="card" style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-bottom: 30px;">
            <div style="font-size: 4em; margin-bottom: 20px;">🎉</div>
            <h2 style="color: white; margin-bottom: 15px;">欢迎使用 SafeBox！</h2>
            <p style="font-size: 1.1em; opacity: 0.9; margin-bottom: 0;">
                这是您第一次使用SafeBox密码管理系统。<br>
                请设置一个强密码来保护您的密码库。
            </p>
        </div>

        <!-- 初始化表单 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">🔑 初始化密码库</h2>
                <p style="color: #666; margin-top: 10px;">
                    设置主密码来创建您的安全密码库
                </p>
            </div>

            <form th:action="@{/web/init}" method="post" th:object="${request}">
                <div class="form-group">
                    <label for="databaseName" class="form-label">密码库名称（可选）</label>
                    <input type="text" id="databaseName" name="databaseName" class="form-control" 
                           th:field="*{databaseName}" placeholder="请输入密码库名称（默认：SafeBox密码库）">
                    <small style="color: #666; font-size: 12px;">为您的密码库起一个容易识别的名称</small>
                </div>

                <div class="form-group">
                    <label for="description" class="form-label">密码库描述（可选）</label>
                    <textarea id="description" name="description" class="form-control" rows="3"
                              th:field="*{description}" placeholder="请输入密码库描述（可选）"></textarea>
                    <small style="color: #666; font-size: 12px;">简单描述这个密码库的用途</small>
                </div>

                <div class="form-group">
                    <label for="masterPassword" class="form-label">主密码 *</label>
                    <div class="password-field">
                        <input type="password" id="masterPassword" name="masterPassword" class="form-control" 
                               th:field="*{masterPassword}" placeholder="请输入主密码（至少6个字符）" required>
                        <button type="button" class="password-toggle">显示</button>
                    </div>
                    <div style="margin-top: 10px;">
                        <button type="button" class="btn btn-secondary btn-sm" onclick="generateMasterPassword()">
                            🎲 生成强密码
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="checkPasswordStrengthUI()">
                            🔍 检查强度
                        </button>
                    </div>
                    <div id="password-strength" style="margin-top: 10px; display: none;">
                        <div style="font-size: 12px; margin-bottom: 5px;">密码强度：<span id="strength-level"></span></div>
                        <div id="strength-bar" style="height: 4px; background: #e1e5e9; border-radius: 2px; overflow: hidden;">
                            <div id="strength-fill" style="height: 100%; transition: all 0.3s ease;"></div>
                        </div>
                        <div id="strength-feedback" style="font-size: 11px; color: #666; margin-top: 5px;"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword" class="form-label">确认主密码 *</label>
                    <div class="password-field">
                        <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" 
                               th:field="*{confirmPassword}" placeholder="请再次输入主密码" required>
                        <button type="button" class="password-toggle">显示</button>
                    </div>
                    <div id="password-match" style="margin-top: 5px; font-size: 12px; display: none;"></div>
                </div>

                <div class="action-buttons">
                    <button type="submit" class="btn btn-primary" style="font-size: 16px; padding: 15px 30px;">
                        🚀 创建密码库
                    </button>
                </div>
            </form>
        </div>

        <!-- 安全提示 -->
        <div class="card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
            <div class="card-header" style="border-color: rgba(255,255,255,0.2);">
                <h3 class="card-title" style="color: white;">🛡️ 安全提示</h3>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: white; margin-bottom: 10px;">✅ 主密码要求</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 8px;">• 至少 6 个字符长度</li>
                        <li style="margin-bottom: 8px;">• 建议包含大小写字母</li>
                        <li style="margin-bottom: 8px;">• 建议包含数字和特殊字符</li>
                        <li style="margin-bottom: 8px;">• 避免使用常见词汇</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: white; margin-bottom: 10px;">🔒 重要提醒</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 8px;">• 请务必记住您的主密码</li>
                        <li style="margin-bottom: 8px;">• 主密码丢失将无法恢复</li>
                        <li style="margin-bottom: 8px;">• 建议将密码写在安全的地方</li>
                        <li style="margin-bottom: 8px;">• 定期更换主密码</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 密码生成器 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🎲 主密码生成器</h3>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div class="form-group">
                    <label for="password-length" class="form-label">密码长度</label>
                    <input type="range" id="password-length" min="12" max="32" value="16" 
                           style="width: 100%;" oninput="updateLengthDisplay(this.value)">
                    <div style="text-align: center; font-size: 14px; margin-top: 5px;">
                        <span id="length-display">16</span> 个字符
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">密码复杂度</label>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="radio" name="complexity" value="high" checked style="margin-right: 8px;">
                            高复杂度（推荐）
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="radio" name="complexity" value="medium" style="margin-right: 8px;">
                            中等复杂度
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="radio" name="complexity" value="readable" style="margin-right: 8px;">
                            易读性优先
                        </label>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button type="button" class="btn btn-success" onclick="generateMasterPassword()">
                    🎲 生成主密码
                </button>
            </div>
        </div>
    </main>

    <!-- 页面底部 -->
    <footer style="text-align: center; padding: 40px 0; color: #666; border-top: 1px solid #e1e5e9; margin-top: 50px;">
        <div class="container">
            <p>&copy; 2025 SafeBox. 基于 Spring Boot + Thymeleaf + KeePass 构建</p>
            <p style="font-size: 0.9em; margin-top: 10px;">
                <span>🔒 安全</span> | 
                <span>🚀 高效</span> | 
                <span>💡 智能</span>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script th:src="@{/js/app.js}"></script>
    
    <script>
        // 更新长度显示
        function updateLengthDisplay(length) {
            document.getElementById('length-display').textContent = length;
        }

        // 生成主密码
        function generateMasterPassword() {
            const length = parseInt(document.getElementById('password-length').value);
            const complexity = document.querySelector('input[name="complexity"]:checked').value;
            
            let charset = '';
            switch (complexity) {
                case 'high':
                    charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
                    break;
                case 'medium':
                    charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
                    break;
                case 'readable':
                    charset = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#$%&*';
                    break;
            }

            let password = '';
            for (let i = 0; i < length; i++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }

            document.getElementById('masterPassword').value = password;
            document.getElementById('confirmPassword').value = '';
            checkPasswordStrengthUI();
            checkPasswordMatch();
            showToast('已生成主密码，请复制到确认密码框', 'success');
        }

        // 检查密码强度UI
        function checkPasswordStrengthUI() {
            const password = document.getElementById('masterPassword').value;
            if (!password) {
                document.getElementById('password-strength').style.display = 'none';
                return;
            }

            const strength = checkPasswordStrength(password);
            const strengthDiv = document.getElementById('password-strength');
            const levelSpan = document.getElementById('strength-level');
            const fillDiv = document.getElementById('strength-fill');
            const feedbackDiv = document.getElementById('strength-feedback');

            strengthDiv.style.display = 'block';
            levelSpan.textContent = strength.level;
            levelSpan.style.color = strength.color;
            fillDiv.style.width = (strength.score * 20) + '%';
            fillDiv.style.backgroundColor = strength.color;

            if (strength.feedback.length > 0) {
                feedbackDiv.textContent = '建议：' + strength.feedback.join('、');
            } else {
                feedbackDiv.textContent = '主密码强度良好！';
            }
        }

        // 检查密码匹配
        function checkPasswordMatch() {
            const masterPassword = document.getElementById('masterPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const matchDiv = document.getElementById('password-match');

            if (!confirmPassword) {
                matchDiv.style.display = 'none';
                return;
            }

            matchDiv.style.display = 'block';
            if (masterPassword === confirmPassword) {
                matchDiv.textContent = '✅ 密码匹配';
                matchDiv.style.color = '#28a745';
            } else {
                matchDiv.textContent = '❌ 密码不匹配';
                matchDiv.style.color = '#dc3545';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 监听密码输入变化
            const masterPasswordInput = document.getElementById('masterPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            
            masterPasswordInput.addEventListener('input', debounce(checkPasswordStrengthUI, 300));
            confirmPasswordInput.addEventListener('input', debounce(checkPasswordMatch, 300));
            masterPasswordInput.addEventListener('input', debounce(checkPasswordMatch, 300));

            // 显示欢迎信息
            setTimeout(() => {
                showToast('欢迎使用SafeBox！请设置一个强密码来保护您的数据。', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
