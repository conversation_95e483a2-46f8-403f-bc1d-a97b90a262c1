package com.newnary.rpa.safebox.controller.web;

import com.newnary.rpa.safebox.dto.SecurityEntry;
import com.newnary.rpa.safebox.service.SecurityManagerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Web控制器测试类
 * 测试Thymeleaf模板渲染和Web界面功能
 *
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@WebMvcTest(WebController.class)
public class WebControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SecurityManagerService securityManagerService;

    private List<SecurityEntry> mockEntries;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        SecurityEntry entry1 = new SecurityEntry();
        entry1.setTitle("测试网站1");
        entry1.setUsername("<EMAIL>");
        entry1.setPassword("password123");

        SecurityEntry entry2 = new SecurityEntry();
        entry2.setTitle("测试网站2");
        entry2.setUsername("<EMAIL>");
        entry2.setPassword("password456");

        mockEntries = Arrays.asList(entry1, entry2);
    }

    /**
     * 测试首页显示
     */
    @Test
    void testIndexPage() throws Exception {
        // Mock服务返回
        when(securityManagerService.getAllEntries()).thenReturn(mockEntries);

        mockMvc.perform(get("/web/"))
                .andExpect(status().isOk())
                .andExpect(view().name("index"))
                .andExpect(model().attributeExists("entries"))
                .andExpect(model().attribute("totalCount", 2))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("密码条目列表")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("测试网站1")))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("测试网站2")));
    }

    /**
     * 测试首页无数据情况
     */
    @Test
    void testIndexPageEmpty() throws Exception {
        // Mock服务返回空列表
        when(securityManagerService.getAllEntries()).thenReturn(Arrays.asList());

        mockMvc.perform(get("/web/"))
                .andExpect(status().isOk())
                .andExpect(view().name("index"))
                .andExpect(model().attribute("totalCount", 0))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("暂无密码条目")));
    }

    /**
     * 测试首页服务异常情况
     */
    @Test
    void testIndexPageWithException() throws Exception {
        // Mock服务抛出异常
        when(securityManagerService.getAllEntries()).thenThrow(new RuntimeException("数据库连接失败"));

        mockMvc.perform(get("/web/"))
                .andExpect(status().isOk())
                .andExpect(view().name("index"))
                .andExpect(model().attributeExists("error"))
                .andExpect(model().attribute("totalCount", 0));
    }

    /**
     * 测试添加密码页面
     */
    @Test
    void testAddPage() throws Exception {
        mockMvc.perform(get("/web/add"))
                .andExpect(status().isOk())
                .andExpect(view().name("add-edit"))
                .andExpect(model().attributeExists("entry"))
                .andExpect(model().attribute("isEdit", false))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("添加密码条目")));
    }

    /**
     * 测试编辑密码页面
     */
    @Test
    void testEditPage() throws Exception {
        // Mock服务返回
        when(securityManagerService.getAllEntries()).thenReturn(mockEntries);

        mockMvc.perform(get("/web/edit/测试网站1"))
                .andExpect(status().isOk())
                .andExpect(view().name("add-edit"))
                .andExpect(model().attributeExists("entry"))
                .andExpect(model().attribute("isEdit", true))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("编辑密码条目")));
    }

    /**
     * 测试编辑不存在的密码条目
     */
    @Test
    void testEditPageNotFound() throws Exception {
        // Mock服务返回
        when(securityManagerService.getAllEntries()).thenReturn(mockEntries);

        mockMvc.perform(get("/web/edit/不存在的网站"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/"));
    }

    /**
     * 测试保存密码条目
     */
    @Test
    void testSaveEntry() throws Exception {
        // Mock服务返回成功
        when(securityManagerService.saveEntry(any(SecurityEntry.class))).thenReturn(true);

        mockMvc.perform(post("/web/save")
                        .param("title", "新网站")
                        .param("username", "<EMAIL>")
                        .param("password", "newpassword123"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/"));
    }

    /**
     * 测试保存密码条目 - 参数验证失败
     */
    @Test
    void testSaveEntryValidationFailure() throws Exception {
        mockMvc.perform(post("/web/save")
                        .param("title", "")  // 空标题
                        .param("username", "<EMAIL>")
                        .param("password", "newpassword123"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/add"));
    }

    /**
     * 测试保存密码条目 - 服务异常
     */
    @Test
    void testSaveEntryServiceException() throws Exception {
        // Mock服务抛出异常
        when(securityManagerService.saveEntry(any(SecurityEntry.class)))
                .thenThrow(new RuntimeException("保存失败"));

        mockMvc.perform(post("/web/save")
                        .param("title", "新网站")
                        .param("username", "<EMAIL>")
                        .param("password", "newpassword123"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/"));
    }

    /**
     * 测试删除密码条目
     */
    @Test
    void testDeleteEntry() throws Exception {
        // Mock服务返回成功
        when(securityManagerService.deleteEntry(anyString())).thenReturn(true);

        mockMvc.perform(post("/web/delete/测试网站1"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/"));
    }

    /**
     * 测试删除不存在的密码条目
     */
    @Test
    void testDeleteEntryNotFound() throws Exception {
        // Mock服务返回失败
        when(securityManagerService.deleteEntry(anyString())).thenReturn(false);

        mockMvc.perform(post("/web/delete/不存在的网站"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/"));
    }

    /**
     * 测试主密码管理页面
     */
    @Test
    void testMasterPasswordPage() throws Exception {
        mockMvc.perform(get("/web/master-password"))
                .andExpect(status().isOk())
                .andExpect(view().name("master-password"))
                .andExpect(content().string(org.hamcrest.Matchers.containsString("主密码管理")));
    }

    /**
     * 测试更新主密码
     */
    @Test
    void testUpdateMasterPassword() throws Exception {
        // Mock服务返回成功
        when(securityManagerService.updateMasterPassword(anyString(), anyString())).thenReturn(true);

        mockMvc.perform(post("/web/master-password")
                        .param("oldPassword", "oldpass123")
                        .param("newPassword", "newpass456")
                        .param("confirmPassword", "newpass456"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/master-password"));
    }

    /**
     * 测试更新主密码 - 密码不匹配
     */
    @Test
    void testUpdateMasterPasswordMismatch() throws Exception {
        mockMvc.perform(post("/web/master-password")
                        .param("oldPassword", "oldpass123")
                        .param("newPassword", "newpass456")
                        .param("confirmPassword", "differentpass"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/master-password"));
    }

    /**
     * 测试更新主密码 - 新旧密码相同
     */
    @Test
    void testUpdateMasterPasswordSame() throws Exception {
        mockMvc.perform(post("/web/master-password")
                        .param("oldPassword", "samepass123")
                        .param("newPassword", "samepass123")
                        .param("confirmPassword", "samepass123"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/master-password"));
    }

    /**
     * 测试更新主密码 - 服务异常
     */
    @Test
    void testUpdateMasterPasswordServiceException() throws Exception {
        // Mock服务抛出异常
        when(securityManagerService.updateMasterPassword(anyString(), anyString()))
                .thenThrow(new RuntimeException("密码更新失败"));

        mockMvc.perform(post("/web/master-password")
                        .param("oldPassword", "oldpass123")
                        .param("newPassword", "newpass456")
                        .param("confirmPassword", "newpass456"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/web/master-password"));
    }
}
