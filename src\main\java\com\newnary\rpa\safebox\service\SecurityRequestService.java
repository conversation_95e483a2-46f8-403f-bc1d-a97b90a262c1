package com.newnary.rpa.safebox.service;

import com.newnary.rpa.safebox.controller.security.request.AskRequest;
import com.newnary.rpa.safebox.controller.security.request.ResultRequest;
import com.newnary.rpa.safebox.controller.security.response.AskResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@Service
@Slf4j
public class SecurityRequestService {

    public String ask(AskRequest request) {

        return null;
    }

    public AskResult result(ResultRequest request) {
        return null;
    }

}
