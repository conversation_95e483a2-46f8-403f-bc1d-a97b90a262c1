# SafeBox 数据库初始化功能指南

## 概述

SafeBox 现在支持数据库初始化功能，允许用户在首次使用时设置主密码并创建密码库。这个功能确保了系统的安全性和易用性。

## 🎯 功能特性

### 1. 自动检测和引导
- **智能检测**：系统启动时自动检测数据库是否已初始化
- **自动重定向**：未初始化时自动引导用户到初始化页面
- **状态检查**：提供 REST API 检查数据库初始化状态

### 2. 用户友好的初始化界面
- **现代化设计**：美观的渐变色界面设计
- **响应式布局**：支持桌面和移动设备
- **实时验证**：密码强度检测和匹配验证
- **智能提示**：详细的安全建议和操作指导

### 3. 强密码生成器
- **多种复杂度**：高复杂度、中等复杂度、易读性优先
- **可调长度**：支持 12-32 位密码长度
- **实时生成**：一键生成强密码
- **强度检测**：实时显示密码强度和改进建议

### 4. 完整的 API 支持
- **REST API**：支持程序化访问和集成
- **Web 界面**：用户友好的图形界面
- **状态管理**：完整的初始化状态管理

## 🚀 使用方式

### Web 界面使用

#### 1. 首次访问
```
访问：http://localhost:8080/web/
```
- 系统会自动检测数据库状态
- 如果未初始化，自动重定向到初始化页面

#### 2. 初始化页面
```
直接访问：http://localhost:8080/web/init
```

**填写信息：**
- **密码库名称**（可选）：为您的密码库起一个名称
- **密码库描述**（可选）：简单描述密码库用途
- **主密码**（必填）：至少 6 个字符的强密码
- **确认主密码**（必填）：再次输入主密码确认

**密码要求：**
- 最少 6 个字符
- 建议包含大小写字母、数字和特殊字符
- 避免使用常见词汇
- 系统提供实时强度检测

#### 3. 密码生成器
- **长度调节**：使用滑块调整密码长度（12-32位）
- **复杂度选择**：
  - 高复杂度：包含所有字符类型（推荐）
  - 中等复杂度：基本字符组合
  - 易读性优先：避免易混淆字符
- **一键生成**：点击"生成主密码"按钮

#### 4. 重置功能
```
访问：http://localhost:8080/web/reset
```
- **危险操作**：将永久删除所有密码数据
- **确认机制**：需要输入 "RESET" 确认
- **安全检查**：多重确认防止误操作

### REST API 使用

#### 1. 检查数据库状态
```http
GET /security-manager/database/status
```

**响应示例：**
```json
{
  "code": 200,
  "message": "成功获取数据库状态",
  "data": false,
  "timestamp": "2025-09-11T18:21:00"
}
```

#### 2. 初始化数据库
```http
POST /security-manager/database/init
Content-Type: application/json

{
  "masterPassword": "MySecurePassword123!",
  "confirmPassword": "MySecurePassword123!",
  "databaseName": "我的密码库",
  "description": "个人使用的安全密码库",
  "forceReinit": false
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "成功初始化密码数据库",
  "data": true,
  "timestamp": "2025-09-11T18:21:00"
}
```

#### 3. 重置数据库
```http
DELETE /security-manager/database/reset
```

**响应示例：**
```json
{
  "code": 200,
  "message": "成功重置密码数据库",
  "data": true,
  "timestamp": "2025-09-11T18:21:00"
}
```

## 🔧 技术实现

### 1. 后端架构

**Service 层：**
- `SecurityManagerService.isDatabaseInitialized()` - 检查初始化状态
- `SecurityManagerService.initializeDatabase()` - 执行初始化
- `SecurityManagerService.resetDatabase()` - 重置数据库

**Controller 层：**
- `SecurityManagerController` - REST API 接口
- `WebController` - Web 页面控制器

**DTO 层：**
- `DatabaseInitRequest` - 初始化请求数据传输对象

### 2. 前端技术

**模板引擎：**
- Thymeleaf 服务端渲染
- 响应式 CSS 设计
- 现代化 JavaScript 交互

**功能特性：**
- 密码强度实时检测
- 表单验证和提示
- 密码生成器
- 复制到剪贴板
- Toast 消息提示

### 3. 安全特性

**密码安全：**
- 最小长度限制
- 强度检测算法
- 安全随机生成
- 内存安全处理

**操作安全：**
- 多重确认机制
- 详细的操作日志
- 异常处理和回滚
- 参数验证和清理

## 📋 使用流程

### 标准初始化流程

1. **启动应用**
   ```bash
   mvn spring-boot:run
   ```

2. **访问系统**
   - 浏览器访问：http://localhost:8080/web/
   - 系统自动检测并重定向到初始化页面

3. **设置主密码**
   - 填写密码库信息（可选）
   - 设置强主密码
   - 确认密码匹配

4. **完成初始化**
   - 点击"创建密码库"
   - 系统创建 KeePass 数据库文件
   - 自动跳转到主界面

5. **开始使用**
   - 添加密码条目
   - 管理现有密码
   - 更改主密码等

### 重置和重新初始化

1. **访问重置页面**
   ```
   http://localhost:8080/web/reset
   ```

2. **确认重置操作**
   - 阅读警告信息
   - 输入确认字符串 "RESET"
   - 勾选确认选项

3. **执行重置**
   - 系统删除现有数据库文件
   - 重定向到初始化页面

4. **重新初始化**
   - 按照标准初始化流程操作
   - 设置新的主密码

## 🛡️ 安全建议

### 主密码安全
- **长度**：建议至少 12 个字符
- **复杂性**：包含大小写字母、数字、特殊字符
- **唯一性**：不要在其他地方使用相同密码
- **备份**：将密码安全地记录在可靠的地方

### 系统安全
- **定期备份**：定期备份 KeePass 数据库文件
- **访问控制**：限制对数据库文件的访问权限
- **网络安全**：在生产环境中使用 HTTPS
- **更新维护**：定期更新系统和依赖库

### 操作安全
- **谨慎重置**：重置操作将永久删除所有数据
- **确认操作**：重要操作前仔细确认
- **日志监控**：关注系统日志中的异常信息
- **权限管理**：合理分配用户权限

## 🔍 故障排除

### 常见问题

**1. 初始化失败**
- 检查磁盘空间是否充足
- 确认数据库文件路径权限
- 查看应用日志获取详细错误信息

**2. 密码验证失败**
- 确认密码长度符合要求
- 检查密码确认是否匹配
- 避免使用特殊字符导致的编码问题

**3. 页面无法访问**
- 确认应用程序正常启动
- 检查端口 8080 是否被占用
- 验证防火墙设置

**4. 数据库文件问题**
- 检查配置文件中的路径设置
- 确认文件系统权限
- 验证 KeePass 库依赖

### 日志分析
```bash
# 查看应用启动日志
mvn spring-boot:run

# 关注关键日志信息
- "数据库未初始化，重定向到初始化页面"
- "成功初始化密码数据库"
- "初始化密码数据库失败"
```

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. **查看日志**：检查应用程序日志获取详细错误信息
2. **检查配置**：验证 `application.yml` 中的配置项
3. **重启应用**：尝试重启应用程序解决临时问题
4. **联系支持**：提供详细的错误信息和操作步骤

---

**SafeBox 数据库初始化功能** - 让密码管理更安全、更简单！
