# Thymeleaf 集成文档

## 概述

本项目已成功集成 Thymeleaf 模板引擎，提供完整的 Web 界面用于密码管理。基于 Spring Boot 2.7.6 和 Thymeleaf 3.x 构建。

## 🎯 功能特性

### 1. Web 界面功能
- **首页** - 显示所有密码条目列表
- **添加密码** - 创建新的密码条目
- **编辑密码** - 修改现有密码条目
- **删除密码** - 删除指定密码条目
- **主密码管理** - 更改 KeePass 数据库主密码

### 2. 用户体验优化
- **响应式设计** - 支持桌面和移动设备
- **密码显示/隐藏** - 点击切换密码可见性
- **一键复制** - 快速复制用户名和密码
- **密码生成器** - 生成安全的随机密码
- **密码强度检测** - 实时检查密码安全性
- **消息提示** - 操作成功/失败的友好提示

### 3. 安全特性
- **参数校验** - 前后端双重验证
- **错误处理** - 完善的异常处理机制
- **安全提示** - 密码管理最佳实践指导

## 🏗️ 技术架构

### 依赖配置

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-thymeleaf</artifactId>
</dependency>
```

### Thymeleaf 配置

```yaml
spring:
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    encoding: UTF-8
    mode: HTML
    cache: false  # 开发环境禁用缓存
    check-template: true
    check-template-location: true
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 0  # 开发环境禁用静态资源缓存
```

### 目录结构

```
src/main/resources/
├── templates/           # Thymeleaf 模板文件
│   ├── layout.html     # 基础布局模板
│   ├── index.html      # 首页模板
│   ├── add-edit.html   # 添加/编辑密码模板
│   └── master-password.html  # 主密码管理模板
└── static/             # 静态资源文件
    ├── css/
    │   └── style.css   # 样式文件
    └── js/
        └── app.js      # JavaScript 文件
```

## 📋 页面说明

### 1. 首页 (`/web/`)
- **功能**: 显示所有密码条目
- **特性**: 
  - 统计信息展示
  - 密码条目表格
  - 密码显示/隐藏切换
  - 一键复制功能
  - 快速操作面板
  - 安全提示信息

### 2. 添加密码 (`/web/add`)
- **功能**: 创建新的密码条目
- **特性**:
  - 表单验证
  - 密码生成器
  - 密码强度检测
  - 自定义密码生成选项

### 3. 编辑密码 (`/web/edit/{title}`)
- **功能**: 修改现有密码条目
- **特性**:
  - 预填充现有数据
  - 标题不可修改（保证唯一性）
  - 密码强度检测

### 4. 主密码管理 (`/web/master-password`)
- **功能**: 更改 KeePass 数据库主密码
- **特性**:
  - 旧密码验证
  - 新密码确认
  - 主密码生成器
  - 安全建议提示

## 🎨 样式设计

### 设计理念
- **现代化**: 使用渐变色和阴影效果
- **简洁**: 清晰的布局和导航
- **友好**: 温和的色彩搭配
- **专业**: 适合企业级应用

### 主要颜色
- **主色调**: `#667eea` (蓝紫色)
- **辅助色**: `#764ba2` (深紫色)
- **成功色**: `#28a745` (绿色)
- **危险色**: `#dc3545` (红色)
- **警告色**: `#ffc107` (黄色)

### 响应式支持
- **桌面**: 1200px+ 最佳体验
- **平板**: 768px-1199px 适配
- **手机**: <768px 移动优化

## 🔧 JavaScript 功能

### 核心功能
1. **密码显示/隐藏切换**
2. **删除确认对话框**
3. **表单验证**
4. **自动隐藏提示消息**
5. **复制到剪贴板**
6. **密码生成器**
7. **密码强度检测**

### 工具函数
- `generatePassword()` - 生成随机密码
- `checkPasswordStrength()` - 检查密码强度
- `showToast()` - 显示提示消息
- `debounce()` - 防抖函数

## 🚀 使用方式

### 1. 启动应用
```bash
mvn spring-boot:run
```

### 2. 访问 Web 界面
- **首页**: http://localhost:8080/web/
- **添加密码**: http://localhost:8080/web/add
- **主密码管理**: http://localhost:8080/web/master-password

### 3. API 接口
所有 Web 页面都通过以下 REST API 与后端交互：
- `GET /security-manager/entries` - 获取密码列表
- `POST /security-manager/entry` - 保存密码条目
- `DELETE /security-manager/entry/{title}` - 删除密码条目
- `PUT /security-manager/master-password` - 更新主密码

## 🧪 测试支持

### Web 控制器测试
- **测试类**: `WebControllerTest`
- **测试覆盖**: 所有 Web 控制器方法
- **测试场景**: 正常流程、异常处理、参数验证

### 测试运行
```bash
mvn test -Dtest=WebControllerTest
```

## 📱 移动端适配

### 响应式特性
- **导航栏**: 移动端垂直布局
- **表格**: 字体缩小，间距调整
- **按钮**: 全宽显示，垂直排列
- **表单**: 优化触摸体验

### 移动端优化
- 触摸友好的按钮大小
- 适合手指操作的间距
- 简化的界面布局
- 快速加载优化

## 🔒 安全考虑

### 前端安全
- **XSS 防护**: Thymeleaf 自动转义
- **CSRF 保护**: Spring Security 集成
- **输入验证**: 前后端双重验证
- **敏感信息**: 密码字段默认隐藏

### 最佳实践
- 定期更换主密码
- 使用强密码策略
- 启用 HTTPS（生产环境）
- 定期备份数据库

## 🎯 后续优化

### 功能增强
- [ ] 密码条目分类管理
- [ ] 批量导入/导出功能
- [ ] 密码历史记录
- [ ] 二次验证支持
- [ ] 密码过期提醒

### 性能优化
- [ ] 前端资源压缩
- [ ] 图片懒加载
- [ ] 缓存策略优化
- [ ] CDN 集成

### 用户体验
- [ ] 暗色主题支持
- [ ] 多语言国际化
- [ ] 键盘快捷键
- [ ] 拖拽排序功能

## 📞 技术支持

如有问题或建议，请联系开发团队或提交 Issue。

---

**SafeBox Web 界面** - 让密码管理更简单、更安全、更高效！
