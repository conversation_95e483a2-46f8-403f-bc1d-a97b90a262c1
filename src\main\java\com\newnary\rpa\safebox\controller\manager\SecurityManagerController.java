package com.newnary.rpa.safebox.controller.manager;

import com.newnary.rpa.safebox.dto.BaseResponse;
import com.newnary.rpa.safebox.dto.DatabaseInitRequest;
import com.newnary.rpa.safebox.dto.MasterPasswordUpdateRequest;
import com.newnary.rpa.safebox.dto.SecurityEntry;
import com.newnary.rpa.safebox.service.SecurityManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 密码管理控制器
 * 提供密码条目的增删改查和主密码管理API
 *
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@RestController
@RequestMapping("/security-manager")
@Slf4j
public class SecurityManagerController {

    @Resource
    private SecurityManagerService securityManagerService;

    /**
     * 获取账号列表
     * 返回所有存储在KeePass数据库中的密码条目
     *
     * @return 包含所有密码条目的响应对象
     */
    @GetMapping("/entries")
    public BaseResponse<List<SecurityEntry>> getEntries() {
        try {
            log.info("接收到获取账号列表请求");
            List<SecurityEntry> entries = securityManagerService.getAllEntries();
            log.info("成功获取{}个账号条目", entries.size());
            return BaseResponse.success("成功获取账号列表", entries);
        } catch (Exception e) {
            log.error("获取账号列表失败：{}", e.getMessage(), e);
            return BaseResponse.fail("获取账号列表失败：" + e.getMessage());
        }
    }

    /**
     * 保存账密(新增或更新)
     * 根据标题判断是新增还是更新操作
     *
     * @param securityEntry 密码条目信息
     * @return 操作结果响应对象
     */
    @PostMapping("/entry")
    public BaseResponse<Boolean> saveEntry(@RequestBody SecurityEntry securityEntry) {
        try {
            log.info("接收到保存账密请求，标题：{}", securityEntry.getTitle());

            // 参数校验
            if (securityEntry.getTitle() == null || securityEntry.getTitle().trim().isEmpty()) {
                return BaseResponse.fail("条目标题不能为空");
            }
            if (securityEntry.getUsername() == null || securityEntry.getUsername().trim().isEmpty()) {
                return BaseResponse.fail("用户名不能为空");
            }
            if (securityEntry.getPassword() == null || securityEntry.getPassword().trim().isEmpty()) {
                return BaseResponse.fail("密码不能为空");
            }

            boolean result = securityManagerService.saveEntry(securityEntry);
            if (result) {
                log.info("成功保存账密，标题：{}", securityEntry.getTitle());
                return BaseResponse.success("成功保存账密", true);
            } else {
                log.warn("保存账密失败，标题：{}", securityEntry.getTitle());
                return BaseResponse.fail("保存账密失败");
            }
        } catch (Exception e) {
            log.error("保存账密失败：{}", e.getMessage(), e);
            return BaseResponse.fail("保存账密失败：" + e.getMessage());
        }
    }

    /**
     * 删除账密
     * 根据标题删除指定的密码条目
     *
     * @param title 要删除的条目标题
     * @return 删除结果响应对象
     */
    @DeleteMapping("/entry/{title}")
    public BaseResponse<Boolean> deleteEntry(@PathVariable String title) {
        try {
            log.info("接收到删除账密请求，标题：{}", title);

            // 参数校验
            if (title == null || title.trim().isEmpty()) {
                return BaseResponse.fail("条目标题不能为空");
            }

            boolean result = securityManagerService.deleteEntry(title);
            if (result) {
                log.info("成功删除账密，标题：{}", title);
                return BaseResponse.success("成功删除账密", true);
            } else {
                log.warn("删除账密失败，可能条目不存在，标题：{}", title);
                return BaseResponse.fail("删除账密失败，条目可能不存在");
            }
        } catch (Exception e) {
            log.error("删除账密失败：{}", e.getMessage(), e);
            return BaseResponse.fail("删除账密失败：" + e.getMessage());
        }
    }

    /**
     * 更新主密码
     * 更改KeePass数据库的主密码
     *
     * @param request 包含旧密码和新密码的请求对象
     * @return 更新结果响应对象
     */
    @PutMapping("/master-password")
    public BaseResponse<Boolean> updateMasterPassword(@RequestBody MasterPasswordUpdateRequest request) {
        try {
            log.info("接收到更新主密码请求");

            // 参数校验
            if (request.getOldPassword() == null || request.getOldPassword().trim().isEmpty()) {
                return BaseResponse.fail("旧密码不能为空");
            }
            if (request.getNewPassword() == null || request.getNewPassword().trim().isEmpty()) {
                return BaseResponse.fail("新密码不能为空");
            }
            if (request.getOldPassword().equals(request.getNewPassword())) {
                return BaseResponse.fail("新密码不能与旧密码相同");
            }

            boolean result = securityManagerService.updateMasterPassword(
                    request.getOldPassword(), request.getNewPassword());
            if (result) {
                log.info("成功更新主密码");
                return BaseResponse.success("成功更新主密码", true);
            } else {
                log.warn("更新主密码失败");
                return BaseResponse.fail("更新主密码失败");
            }
        } catch (Exception e) {
            log.error("更新主密码失败：{}", e.getMessage(), e);
            return BaseResponse.fail("更新主密码失败：" + e.getMessage());
        }
    }

    /**
     * 检查数据库初始化状态
     * 检查KeePass数据库是否已经初始化
     *
     * @return 包含初始化状态的响应对象
     */
    @GetMapping("/database/status")
    public BaseResponse<Boolean> getDatabaseStatus() {
        try {
            log.info("接收到检查数据库状态请求");
            boolean isInitialized = securityManagerService.isDatabaseInitialized();
            log.info("数据库初始化状态：{}", isInitialized ? "已初始化" : "未初始化");
            return BaseResponse.success("成功获取数据库状态", isInitialized);
        } catch (Exception e) {
            log.error("检查数据库状态失败：{}", e.getMessage(), e);
            return BaseResponse.fail("检查数据库状态失败：" + e.getMessage());
        }
    }

    /**
     * 初始化密码数据库
     * 创建新的KeePass数据库并设置主密码
     *
     * @param request 初始化请求，包含主密码等信息
     * @return 初始化结果响应对象
     */
    @PostMapping("/database/init")
    public BaseResponse<Boolean> initializeDatabase(@RequestBody DatabaseInitRequest request) {
        try {
            log.info("接收到初始化数据库请求");

            // 参数校验
            if (request == null) {
                return BaseResponse.fail("初始化请求不能为空");
            }
            if (request.getMasterPassword() == null || request.getMasterPassword().trim().isEmpty()) {
                return BaseResponse.fail("主密码不能为空");
            }
            if (request.getConfirmPassword() == null || request.getConfirmPassword().trim().isEmpty()) {
                return BaseResponse.fail("确认密码不能为空");
            }
            if (!request.isPasswordMatched()) {
                return BaseResponse.fail("密码确认不匹配");
            }
            if (request.getMasterPassword().length() < 6) {
                return BaseResponse.fail("主密码长度不能少于6个字符");
            }

            boolean result = securityManagerService.initializeDatabase(request);
            if (result) {
                log.info("成功初始化密码数据库");
                return BaseResponse.success("成功初始化密码数据库", true);
            } else {
                log.warn("初始化密码数据库失败");
                return BaseResponse.fail("初始化密码数据库失败");
            }
        } catch (IllegalArgumentException e) {
            log.warn("初始化数据库参数错误：{}", e.getMessage());
            return BaseResponse.fail(e.getMessage());
        } catch (Exception e) {
            log.error("初始化密码数据库失败：{}", e.getMessage(), e);
            return BaseResponse.fail("初始化密码数据库失败：" + e.getMessage());
        }
    }

    /**
     * 重置密码数据库
     * 删除现有数据库文件，为重新初始化做准备
     * 注意：此操作将删除所有存储的密码数据，请谨慎使用
     *
     * @return 重置结果响应对象
     */
    @DeleteMapping("/database/reset")
    public BaseResponse<Boolean> resetDatabase() {
        try {
            log.info("接收到重置数据库请求");
            boolean result = securityManagerService.resetDatabase();
            if (result) {
                log.info("成功重置密码数据库");
                return BaseResponse.success("成功重置密码数据库", true);
            } else {
                log.warn("重置密码数据库失败");
                return BaseResponse.fail("重置密码数据库失败");
            }
        } catch (Exception e) {
            log.error("重置密码数据库失败：{}", e.getMessage(), e);
            return BaseResponse.fail("重置密码数据库失败：" + e.getMessage());
        }
    }
}
