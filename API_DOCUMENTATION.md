# 密码管理API文档

## 概述

本API提供了基于KeePass数据库的密码管理功能，包括密码条目的增删改查和主密码管理。

## 基础信息

- **基础URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **数据库**: KeePass (Database2.kdbx)
- **默认密码**: 123123

## API接口

### 1. 获取账号列表

**接口地址**: `GET /security-manager/entries`

**功能描述**: 获取KeePass数据库中所有的密码条目

**请求参数**: 无

**响应示例**:
```json
{
  "state": "SUCCESS",
  "message": "成功获取账号列表",
  "data": [
    {
      "title": "This is a test A",
      "username": "admin",
      "password": "adminA"
    },
    {
      "title": "This is a test B", 
      "username": "root",
      "password": "rootB"
    }
  ]
}
```

### 2. 保存账密(新增或更新)

**接口地址**: `POST /security-manager/entry`

**功能描述**: 根据标题判断是新增还是更新密码条目

**请求参数**:
```json
{
  "title": "条目标题",
  "username": "用户名",
  "password": "密码"
}
```

**响应示例**:
```json
{
  "state": "SUCCESS",
  "message": "成功保存账密",
  "data": true
}
```

**参数校验**:
- `title`: 必填，不能为空
- `username`: 必填，不能为空  
- `password`: 必填，不能为空

### 3. 删除账密

**接口地址**: `DELETE /security-manager/entry/{title}`

**功能描述**: 根据标题删除指定的密码条目

**路径参数**:
- `title`: 要删除的条目标题

**响应示例**:
```json
{
  "state": "SUCCESS",
  "message": "成功删除账密",
  "data": true
}
```

**错误响应**:
```json
{
  "state": "FAIL",
  "message": "删除账密失败，条目可能不存在",
  "data": null
}
```

### 4. 更新主密码

**接口地址**: `PUT /security-manager/master-password`

**功能描述**: 更改KeePass数据库的主密码

**请求参数**:
```json
{
  "oldPassword": "旧密码",
  "newPassword": "新密码"
}
```

**响应示例**:
```json
{
  "state": "SUCCESS",
  "message": "成功更新主密码",
  "data": true
}
```

**参数校验**:
- `oldPassword`: 必填，不能为空
- `newPassword`: 必填，不能为空，且不能与旧密码相同

## 错误处理

所有API都使用统一的响应格式：

**成功响应**:
```json
{
  "state": "SUCCESS",
  "message": "操作成功消息",
  "data": "响应数据"
}
```

**失败响应**:
```json
{
  "state": "FAIL", 
  "message": "错误消息",
  "data": null
}
```

## 测试用例

### 使用curl测试

1. **获取账号列表**:
```bash
curl -X GET http://localhost:8080/security-manager/entries
```

2. **添加新账号**:
```bash
curl -X POST http://localhost:8080/security-manager/entry \
  -H "Content-Type: application/json" \
  -d '{"title":"测试账号","username":"testuser","password":"testpass"}'
```

3. **删除账号**:
```bash
curl -X DELETE http://localhost:8080/security-manager/entry/测试账号
```

4. **更新主密码**:
```bash
curl -X PUT http://localhost:8080/security-manager/master-password \
  -H "Content-Type: application/json" \
  -d '{"oldPassword":"123123","newPassword":"newpass123"}'
```

## 注意事项

1. **数据库文件路径**: 配置在`application.yml`中的`safebox.keepass.kdbxPath`
2. **默认密码**: 当前硬编码为"123123"，生产环境应从配置文件或环境变量读取
3. **线程安全**: 当前实现未考虑并发访问，生产环境需要添加适当的同步机制
4. **备份**: 建议在更新主密码前备份数据库文件

## 技术实现

- **框架**: Spring Boot 2.7.6
- **KeePass库**: KeePassJava2 2.2.4
- **数据格式**: KDBX v4
- **加密算法**: AES, Argon2-d, ChaCha20
