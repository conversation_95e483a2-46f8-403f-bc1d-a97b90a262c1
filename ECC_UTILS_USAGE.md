# ECC工具类使用说明

## 概述

`ECCUtils` 是一个基于 Bouncy Castle 库实现的椭圆曲线加密工具类，提供完整的 ECC 加密解密、数字签名功能。

## 主要功能

1. **密钥对生成** - 支持多种椭圆曲线（secp256r1, secp384r1, secp521r1）
2. **加密解密** - 使用 ECIES 方案进行数据加密
3. **数字签名** - 使用 ECDSA 算法进行数字签名和验证
4. **密钥管理** - 支持密钥的序列化和反序列化

## 使用示例

### 1. 生成密钥对

```java
// 使用默认曲线 secp256r1
ECCUtils.ECCKeyPair keyPair = ECCUtils.generateKeyPair();

// 使用指定曲线
ECCUtils.ECCKeyPair keyPair384 = ECCUtils.generateKeyPair("secp384r1");

// 获取密钥的不同格式
String privateKeyHex = keyPair.getPrivateKeyHex();
String publicKeyHex = keyPair.getPublicKeyHex();
String privateKeyBase64 = keyPair.getPrivateKeyBase64();
String publicKeyBase64 = keyPair.getPublicKeyBase64();
```

### 2. 密钥恢复

```java
// 从十六进制字符串恢复密钥
ECPrivateKeyParameters privateKey = ECCUtils.loadPrivateKeyFromHex(privateKeyHex, "secp256r1");
ECPublicKeyParameters publicKey = ECCUtils.loadPublicKeyFromHex(publicKeyHex, "secp256r1");
```

### 3. 加密解密

```java
// 生成两个密钥对
ECCUtils.ECCKeyPair senderKeyPair = ECCUtils.generateKeyPair();
ECCUtils.ECCKeyPair receiverKeyPair = ECCUtils.generateKeyPair();

// 加密消息（使用接收方公钥）
String message = "Hello, ECC Encryption!";
String encryptedMessage = ECCUtils.encrypt(message, receiverKeyPair.getPublicKey());

// 解密消息（使用接收方私钥）
String decryptedMessage = ECCUtils.decrypt(encryptedMessage, receiverKeyPair.getPrivateKey());
```

### 4. 数字签名

```java
// 生成签名者密钥对
ECCUtils.ECCKeyPair signerKeyPair = ECCUtils.generateKeyPair();

// 对消息进行签名
String message = "This is a message to be signed.";
ECCUtils.ECCSignature signature = ECCUtils.sign(message, signerKeyPair.getPrivateKey());

// 验证签名
boolean isValid = ECCUtils.verify(message, signature, signerKeyPair.getPublicKey());

// 获取签名的不同格式
String signatureHex = signature.toHexString();
String signatureBase64 = signature.toBase64String();
```

### 5. 签名序列化和反序列化

```java
// 从十六进制字符串恢复签名
ECCUtils.ECCSignature recoveredSignature = ECCUtils.loadSignatureFromHex(signatureHex);

// 从Base64字符串恢复签名
ECCUtils.ECCSignature recoveredSignature2 = ECCUtils.loadSignatureFromBase64(signatureBase64);

// 验证恢复的签名
boolean isValid = ECCUtils.verify(message, recoveredSignature, signerKeyPair.getPublicKey());
```

## 支持的椭圆曲线

- **secp256r1** (P-256) - 默认，256位，NIST推荐
- **secp384r1** (P-384) - 384位，更高安全性
- **secp521r1** (P-521) - 521位，最高安全性

## 安全特性

1. **强随机数生成** - 使用 `SecureRandom` 确保密钥安全性
2. **标准算法** - 使用 ECDSA 数字签名和 ECIES 加密方案
3. **哈希算法** - 使用 SHA-256 进行消息摘要
4. **密钥格式** - 支持压缩公钥格式，节省存储空间

## 性能特点

- **高效** - ECC 相比 RSA 具有更小的密钥长度和更快的运算速度
- **安全** - 256位 ECC 密钥提供与 3072位 RSA 相当的安全强度
- **紧凑** - 密钥和签名长度较短，适合移动和嵌入式应用

## 注意事项

1. **依赖库** - 需要 Bouncy Castle 库支持
2. **字符编码** - 字符串加密使用 UTF-8 编码
3. **异常处理** - 所有方法都会抛出 `RuntimeException`，需要适当处理
4. **线程安全** - 工具类方法是线程安全的

## 测试验证

运行 `ECCUtils.main()` 方法可以执行完整的功能测试，包括：

- 密钥对生成测试
- 密钥恢复测试  
- 加密解密测试
- 数字签名测试
- 签名编码解码测试
- 多种椭圆曲线测试

## 实际应用场景

1. **安全通信** - 端到端加密通信
2. **数字证书** - PKI 体系中的证书签名
3. **区块链** - 数字货币的交易签名
4. **物联网** - 设备身份认证和数据保护
5. **移动应用** - 轻量级的加密解决方案

## 示例输出

```
=== ECC工具类测试开始 ===

1. 测试密钥对生成
密钥对1 - 私钥: 4b38c4b966b0da69740edb1d5ae34ed3f6a47ba42614f5abda88cf6d91189470
密钥对1 - 公钥: 02f1c92b628f4741fe7e3d9f406a1e62067d938ce7def577d6c695f378f49ebb8e
✓ 密钥对生成测试通过

2. 测试密钥恢复
原始私钥: 4b38c4b966b0da69740edb1d5ae34ed3f6a47ba42614f5abda88cf6d91189470
恢复私钥: 4b38c4b966b0da69740edb1d5ae34ed3f6a47ba42614f5abda88cf6d91189470
密钥恢复匹配: true
✓ 密钥恢复测试通过

3. 测试加密解密
原始消息: Hello, ECC Encryption! 这是一条测试消息。
加密后消息: A0KVcCIaDweUEsQVOTbh9pvZMVyO8zsIbufj7rsPtb2vd7tJ3mdFPcNE2tipiCXE8k+qTN1mSD1uuAAedEmiDgvZQ4RUveL1KZJ/TmQAxxlov1w=
解密后消息: Hello, ECC Encryption! 这是一条测试消息。
加密解密匹配: true
✓ 加密解密测试通过

=== 所有测试完成，ECC工具类功能正常 ===
```
