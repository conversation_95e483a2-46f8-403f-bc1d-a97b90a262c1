package com.newnary.rpa.safebox.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.newnary.rpa.safebox.dto.MasterPasswordUpdateRequest;
import com.newnary.rpa.safebox.dto.SecurityEntry;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SecurityManager集成测试
 *
 * <AUTHOR>
 * @since Created on 2025-09-11
 **/
@SpringBootTest
@AutoConfigureWebMvc
class SecurityManagerIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    /**
     * 测试完整的API流程
     */
    @Test
    void testCompleteApiFlow() throws Exception {
        setUp();
        
        // 1. 获取初始账号列表
        mockMvc.perform(get("/security-manager/entries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"));

        // 2. 添加新账号
        SecurityEntry newEntry = new SecurityEntry();
        newEntry.setTitle("Test API Entry");
        newEntry.setUsername("testuser");
        newEntry.setPassword("testpassword");

        mockMvc.perform(post("/security-manager/entry")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newEntry)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("成功保存账密"));

        // 3. 再次获取账号列表，验证新增成功
        mockMvc.perform(get("/security-manager/entries"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"));

        // 4. 更新账号信息
        newEntry.setPassword("updatedpassword");
        mockMvc.perform(post("/security-manager/entry")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newEntry)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"));

        // 5. 删除账号
        mockMvc.perform(delete("/security-manager/entry/Test API Entry"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("SUCCESS"))
                .andExpect(jsonPath("$.message").value("成功删除账密"));
    }

    /**
     * 测试参数校验
     */
    @Test
    void testParameterValidation() throws Exception {
        setUp();
        
        // 测试空标题
        SecurityEntry invalidEntry = new SecurityEntry();
        invalidEntry.setUsername("testuser");
        invalidEntry.setPassword("testpassword");

        mockMvc.perform(post("/security-manager/entry")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidEntry)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("FAIL"))
                .andExpect(jsonPath("$.message").value("条目标题不能为空"));

        // 测试删除不存在的条目
        mockMvc.perform(delete("/security-manager/entry/NonExistentEntry"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("FAIL"));
    }

    /**
     * 测试主密码更新（注意：这个测试可能会影响实际数据库）
     */
    @Test
    void testMasterPasswordUpdate() throws Exception {
        setUp();
        
        MasterPasswordUpdateRequest request = new MasterPasswordUpdateRequest();
        request.setOldPassword("wrongpassword");
        request.setNewPassword("newpassword");

        // 测试错误的旧密码
        mockMvc.perform(put("/security-manager/master-password")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("FAIL"));

        // 测试相同密码
        request.setOldPassword("123123");
        request.setNewPassword("123123");
        mockMvc.perform(put("/security-manager/master-password")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.state").value("FAIL"))
                .andExpect(jsonPath("$.message").value("新密码不能与旧密码相同"));
    }
}
