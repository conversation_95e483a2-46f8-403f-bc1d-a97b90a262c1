package com.newnary.rpa.safebox.dto;

import com.newnary.rpa.safebox.constant.ResponseConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用响应对象
 * <AUTHOR>
 * @since Created on 2025-09-10
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseResponse<T> {

    /**
     * 状态: SUCCESS / FAIL
     **/
    private String state;

    /**
     * 响应消息
     **/
    private String message;

    /**
     * 响应数据
     **/
    private T data;

    /**
     * 创建成功响应
     * @param data 响应数据
     * @return 成功响应对象
     */
    public static <T> BaseResponse<T> success(T data) {
        return BaseResponse.<T>builder()
                .state(ResponseConstant.State.SUCCESS)
                .message(ResponseConstant.Message.SUCCESS)
                .data(data)
                .build();
    }

    /**
     * 创建成功响应
     * @param message 成功消息
     * @param data 响应数据
     * @return 成功响应对象
     */
    public static <T> BaseResponse<T> success(String message, T data) {
        return BaseResponse.<T>builder()
                .state(ResponseConstant.State.SUCCESS)
                .message(message)
                .data(data)
                .build();
    }

    /**
     * 创建失败响应
     * @param message 错误消息
     * @return 失败响应对象
     */
    public static <T> BaseResponse<T> fail(String message) {
        return BaseResponse.<T>builder()
                .state(ResponseConstant.State.FAIL)
                .message(message)
                .build();
    }

    /**
     * 创建失败响应
     * @param message 错误消息
     * @param data 响应数据
     * @return 失败响应对象
     */
    public static <T> BaseResponse<T> fail(String message, T data) {
        return BaseResponse.<T>builder()
                .state(ResponseConstant.State.FAIL)
                .message(message)
                .data(data)
                .build();
    }

    /**
     * 创建自定义响应
     * @param state 状态
     * @param message 消息
     * @param data 数据
     * @return 自定义响应对象
     */
    public static <T> BaseResponse<T> custom(String state, String message, T data) {
        return BaseResponse.<T>builder()
                .state(state)
                .message(message)
                .data(data)
                .build();
    }
}
