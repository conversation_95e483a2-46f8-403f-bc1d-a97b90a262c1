/**
 * SafeBox Web应用JavaScript
 * 提供前端交互功能
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('SafeBox Web应用已加载');
    
    // 初始化所有功能
    initPasswordToggle();
    initDeleteConfirm();
    initFormValidation();
    initAutoHideAlerts();
    initCopyToClipboard();
});

/**
 * 初始化密码显示/隐藏功能
 */
function initPasswordToggle() {
    const passwordFields = document.querySelectorAll('.password-field');
    
    passwordFields.forEach(field => {
        const input = field.querySelector('input[type="password"], input[type="text"]');
        const toggle = field.querySelector('.password-toggle');
        
        if (input && toggle) {
            toggle.addEventListener('click', function() {
                if (input.type === 'password') {
                    input.type = 'text';
                    toggle.textContent = '隐藏';
                } else {
                    input.type = 'password';
                    toggle.textContent = '显示';
                }
            });
        }
    });
}

/**
 * 初始化删除确认功能
 */
function initDeleteConfirm() {
    const deleteButtons = document.querySelectorAll('.btn-delete');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const title = this.getAttribute('data-title') || '该条目';
            const confirmMessage = `确定要删除密码条目"${title}"吗？此操作不可撤销。`;
            
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return false;
            }
        });
    });
}

/**
 * 初始化表单验证
 */
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');
                    showFieldError(field, '此字段不能为空');
                } else {
                    field.classList.remove('error');
                    hideFieldError(field);
                }
            });
            
            // 密码确认验证
            const newPassword = form.querySelector('input[name="newPassword"]');
            const confirmPassword = form.querySelector('input[name="confirmPassword"]');
            
            if (newPassword && confirmPassword) {
                if (newPassword.value !== confirmPassword.value) {
                    isValid = false;
                    confirmPassword.classList.add('error');
                    showFieldError(confirmPassword, '密码确认不匹配');
                } else {
                    confirmPassword.classList.remove('error');
                    hideFieldError(confirmPassword);
                }
            }
            
            if (!isValid) {
                e.preventDefault();
                return false;
            }
        });
    });
}

/**
 * 显示字段错误信息
 */
function showFieldError(field, message) {
    hideFieldError(field); // 先清除已有错误
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.color = '#dc3545';
    errorDiv.style.fontSize = '12px';
    errorDiv.style.marginTop = '5px';
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * 隐藏字段错误信息
 */
function hideFieldError(field) {
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * 初始化自动隐藏提示信息
 */
function initAutoHideAlerts() {
    const alerts = document.querySelectorAll('.alert');
    
    alerts.forEach(alert => {
        // 5秒后自动隐藏
        setTimeout(() => {
            alert.style.opacity = '0';
            alert.style.transition = 'opacity 0.5s ease';
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 500);
        }, 5000);
        
        // 点击关闭
        alert.addEventListener('click', function() {
            this.style.opacity = '0';
            this.style.transition = 'opacity 0.3s ease';
            
            setTimeout(() => {
                if (this.parentNode) {
                    this.parentNode.removeChild(this);
                }
            }, 300);
        });
    });
}

/**
 * 初始化复制到剪贴板功能
 */
function initCopyToClipboard() {
    const copyButtons = document.querySelectorAll('.btn-copy');
    
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const text = this.getAttribute('data-text');
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showToast('已复制到剪贴板', 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    showToast('复制失败', 'error');
                });
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                
                try {
                    document.execCommand('copy');
                    showToast('已复制到剪贴板', 'success');
                } catch (err) {
                    console.error('复制失败:', err);
                    showToast('复制失败', 'error');
                }
                
                document.body.removeChild(textArea);
            }
        });
    });
}

/**
 * 显示Toast提示
 */
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 样式
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.padding = '12px 20px';
    toast.style.borderRadius = '6px';
    toast.style.color = 'white';
    toast.style.fontSize = '14px';
    toast.style.zIndex = '9999';
    toast.style.opacity = '0';
    toast.style.transition = 'opacity 0.3s ease';
    
    // 根据类型设置背景色
    switch (type) {
        case 'success':
            toast.style.backgroundColor = '#28a745';
            break;
        case 'error':
            toast.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            toast.style.backgroundColor = '#ffc107';
            toast.style.color = '#212529';
            break;
        default:
            toast.style.backgroundColor = '#17a2b8';
    }
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

/**
 * 生成随机密码
 */
function generatePassword(length = 16) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    let password = '';
    
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    return password;
}

/**
 * 密码强度检测
 */
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    if (password.length >= 8) strength++;
    else feedback.push('至少8个字符');
    
    if (/[a-z]/.test(password)) strength++;
    else feedback.push('包含小写字母');
    
    if (/[A-Z]/.test(password)) strength++;
    else feedback.push('包含大写字母');
    
    if (/[0-9]/.test(password)) strength++;
    else feedback.push('包含数字');
    
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    else feedback.push('包含特殊字符');
    
    const levels = ['很弱', '弱', '一般', '强', '很强'];
    const colors = ['#dc3545', '#fd7e14', '#ffc107', '#28a745', '#20c997'];
    
    return {
        score: strength,
        level: levels[strength] || '很弱',
        color: colors[strength] || '#dc3545',
        feedback: feedback
    };
}

/**
 * 格式化日期时间
 */
function formatDateTime(date) {
    if (!(date instanceof Date)) {
        date = new Date(date);
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
