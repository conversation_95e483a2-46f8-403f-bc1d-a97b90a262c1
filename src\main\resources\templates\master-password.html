<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主密码管理 - SafeBox</title>

    <!-- CSS样式 -->
    <link rel="stylesheet" th:href="@{/css/style.css}">

    <!-- 网站图标 -->
    <link rel="icon" type="image/x-icon" th:href="@{/favicon.ico}">
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="container">
            <h1>🔐 SafeBox</h1>
            <p class="subtitle">安全密码管理系统</p>
        </div>
    </header>

    <!-- 导航栏 -->
    <nav class="nav">
        <div class="container">
            <ul>
                <li><a th:href="@{/web/}">🏠 首页</a></li>
                <li><a th:href="@{/web/add}">➕ 添加密码</a></li>
                <li><a th:href="@{/web/master-password}" class="active">🔑 主密码管理</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container">
        <!-- 消息提示区域 -->
        <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
        <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>
        <div th:if="${info}" class="alert alert-info" th:text="${info}"></div>

        <!-- 页面内容 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">🔑 主密码管理</h2>
                <p style="color: #666; margin-top: 10px;">
                    主密码用于保护您的 KeePass 数据库，请妥善保管并定期更换
                </p>
            </div>

            <form th:action="@{/web/master-password}" method="post">
                <div class="form-group">
                    <label for="oldPassword" class="form-label">当前主密码 *</label>
                    <div class="password-field">
                        <input type="password" id="oldPassword" name="oldPassword" class="form-control" 
                               placeholder="请输入当前主密码" required>
                        <button type="button" class="password-toggle">显示</button>
                    </div>
                    <small style="color: #666; font-size: 12px;">请输入当前正在使用的主密码</small>
                </div>

                <div class="form-group">
                    <label for="newPassword" class="form-label">新主密码 *</label>
                    <div class="password-field">
                        <input type="password" id="newPassword" name="newPassword" class="form-control" 
                               placeholder="请输入新主密码" required>
                        <button type="button" class="password-toggle">显示</button>
                    </div>
                    <div style="margin-top: 10px;">
                        <button type="button" class="btn btn-secondary btn-sm" onclick="generateMasterPassword()">
                            🎲 生成强密码
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="checkNewPasswordStrength()">
                            🔍 检查强度
                        </button>
                    </div>
                    <div id="new-password-strength" style="margin-top: 10px; display: none;">
                        <div style="font-size: 12px; margin-bottom: 5px;">密码强度：<span id="new-strength-level"></span></div>
                        <div id="new-strength-bar" style="height: 4px; background: #e1e5e9; border-radius: 2px; overflow: hidden;">
                            <div id="new-strength-fill" style="height: 100%; transition: all 0.3s ease;"></div>
                        </div>
                        <div id="new-strength-feedback" style="font-size: 11px; color: #666; margin-top: 5px;"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword" class="form-label">确认新主密码 *</label>
                    <div class="password-field">
                        <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" 
                               placeholder="请再次输入新主密码" required>
                        <button type="button" class="password-toggle">显示</button>
                    </div>
                    <div id="password-match" style="margin-top: 5px; font-size: 12px; display: none;"></div>
                </div>

                <div class="action-buttons">
                    <a th:href="@{/web/}" class="btn btn-secondary">❌ 取消</a>
                    <button type="submit" class="btn btn-primary">🔄 更新主密码</button>
                </div>
            </form>
        </div>

        <!-- 安全建议 -->
        <div class="card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
            <div class="card-header" style="border-color: rgba(255,255,255,0.2);">
                <h3 class="card-title" style="color: white;">🛡️ 主密码安全建议</h3>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="color: white; margin-bottom: 10px;">✅ 强密码特征</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 8px;">• 至少 12 个字符长度</li>
                        <li style="margin-bottom: 8px;">• 包含大小写字母</li>
                        <li style="margin-bottom: 8px;">• 包含数字和特殊字符</li>
                        <li style="margin-bottom: 8px;">• 避免使用常见词汇</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color: white; margin-bottom: 10px;">🔒 安全实践</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: 8px;">• 定期更换主密码</li>
                        <li style="margin-bottom: 8px;">• 不要与他人分享</li>
                        <li style="margin-bottom: 8px;">• 使用密码管理器记住</li>
                        <li style="margin-bottom: 8px;">• 启用二次验证（如可用）</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 密码生成器 -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">🎲 主密码生成器</h3>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div class="form-group">
                    <label for="master-password-length" class="form-label">密码长度</label>
                    <input type="range" id="master-password-length" min="12" max="64" value="20" 
                           style="width: 100%;" oninput="updateMasterLengthDisplay(this.value)">
                    <div style="text-align: center; font-size: 14px; margin-top: 5px;">
                        <span id="master-length-display">20</span> 个字符
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">密码复杂度</label>
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="radio" name="complexity" value="high" checked style="margin-right: 8px;">
                            高复杂度（推荐）
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="radio" name="complexity" value="medium" style="margin-right: 8px;">
                            中等复杂度
                        </label>
                        <label style="display: flex; align-items: center; font-weight: normal;">
                            <input type="radio" name="complexity" value="readable" style="margin-right: 8px;">
                            易读性优先
                        </label>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button type="button" class="btn btn-success" onclick="generateMasterPassword()">
                    🎲 生成主密码
                </button>
            </div>
        </div>

        <!-- 重要提醒 -->
        <div class="card" style="background: #fff3cd; border-left: 4px solid #ffc107;">
            <div class="card-header">
                <h3 class="card-title" style="color: #856404;">⚠️ 重要提醒</h3>
            </div>
            <div style="color: #856404;">
                <p><strong>请务必记住您的主密码！</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>主密码丢失将导致无法访问所有存储的密码</li>
                    <li>系统不会存储您的主密码，无法为您找回</li>
                    <li>建议将主密码写在安全的地方作为备份</li>
                    <li>更换主密码前请确保当前密码输入正确</li>
                </ul>
            </div>
        </div>
    </main>

    <!-- 页面底部 -->
    <footer style="text-align: center; padding: 40px 0; color: #666; border-top: 1px solid #e1e5e9; margin-top: 50px;">
        <div class="container">
            <p>&copy; 2025 SafeBox. 基于 Spring Boot + Thymeleaf + KeePass 构建</p>
            <p style="font-size: 0.9em; margin-top: 10px;">
                <span>🔒 安全</span> |
                <span>🚀 高效</span> |
                <span>💡 智能</span>
            </p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script th:src="@{/js/app.js}"></script>

    <script>
        // 更新主密码长度显示
        function updateMasterLengthDisplay(length) {
            document.getElementById('master-length-display').textContent = length;
        }

        // 生成主密码
        function generateMasterPassword() {
            const length = parseInt(document.getElementById('master-password-length').value);
            const complexity = document.querySelector('input[name="complexity"]:checked').value;

            let charset = '';
            switch (complexity) {
                case 'high':
                    charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
                    break;
                case 'medium':
                    charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
                    break;
                case 'readable':
                    charset = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#$%&*';
                    break;
            }

            let password = '';
            for (let i = 0; i < length; i++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }

            document.getElementById('newPassword').value = password;
            document.getElementById('confirmPassword').value = '';
            checkNewPasswordStrength();
            checkPasswordMatch();
            showToast('已生成主密码，请复制到确认密码框', 'success');
        }

        // 检查新密码强度
        function checkNewPasswordStrength() {
            const password = document.getElementById('newPassword').value;
            if (!password) {
                document.getElementById('new-password-strength').style.display = 'none';
                return;
            }

            const strength = checkPasswordStrength(password);
            const strengthDiv = document.getElementById('new-password-strength');
            const levelSpan = document.getElementById('new-strength-level');
            const fillDiv = document.getElementById('new-strength-fill');
            const feedbackDiv = document.getElementById('new-strength-feedback');

            strengthDiv.style.display = 'block';
            levelSpan.textContent = strength.level;
            levelSpan.style.color = strength.color;
            fillDiv.style.width = (strength.score * 20) + '%';
            fillDiv.style.backgroundColor = strength.color;

            if (strength.feedback.length > 0) {
                feedbackDiv.textContent = '建议：' + strength.feedback.join('、');
            } else {
                feedbackDiv.textContent = '主密码强度良好！';
            }
        }

        // 检查密码匹配
        function checkPasswordMatch() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const matchDiv = document.getElementById('password-match');

            if (!confirmPassword) {
                matchDiv.style.display = 'none';
                return;
            }

            matchDiv.style.display = 'block';
            if (newPassword === confirmPassword) {
                matchDiv.textContent = '✅ 密码匹配';
                matchDiv.style.color = '#28a745';
            } else {
                matchDiv.textContent = '❌ 密码不匹配';
                matchDiv.style.color = '#dc3545';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 监听密码输入变化
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');

            newPasswordInput.addEventListener('input', debounce(checkNewPasswordStrength, 300));
            confirmPasswordInput.addEventListener('input', debounce(checkPasswordMatch, 300));
            newPasswordInput.addEventListener('input', debounce(checkPasswordMatch, 300));
        });
    </script>
</body>
</html>
